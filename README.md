# 🏥 HealthCoach AI - Complete AI-Powered Health Management Platform

A comprehensive AI-powered health coach application that integrates wearables, predicts diabetes/hypertension risk, provides explainable AI insights, and offers personalized health recommendations.

## 🌟 Features

### 🤖 AI-Powered Risk Assessment
- **Diabetes Risk Prediction** with ensemble ML models (Logistic Regression + XGBoost)
- **Hypertension Risk Assessment** with clinical-grade accuracy
- **SHAP Explainable AI** for transparent risk factor analysis
- **Trust Score Display** showing confidence and fairness metrics

### 📱 Patient Portal
- **Interactive Risk Cards** with real-time health status
- **Personalized Recommendations** powered by OpenAI GPT
- **Gamification System** with points, levels, badges, and challenges
- **Voice Assistant** for hands-free data entry and interaction
- **Wearable Integration** (Fitbit, Dexcom, Google Fit, Apple Health)

### 👨‍⚕️ Clinician Dashboard
- **Population Health Management** with risk trend analysis
- **Patient Risk Monitoring** with automated alerts
- **SHAP Visualization** for clinical decision support
- **Comprehensive Reports** with PDF generation
- **Care Team Coordination** tools

### 🔒 Security & Compliance
- **JWT Authentication** with secure token management
- **OAuth2 Integration** for wearable device connections
- **Data Encryption** for sensitive health information
- **Audit Logging** for compliance and monitoring
- **Consent Management** for data sharing preferences

### 🎮 Gamification & Engagement
- **Achievement System** with badges and milestones
- **Streak Tracking** for consistent health monitoring
- **Leaderboards** for community motivation
- **Challenge System** with personalized health goals

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Node.js 16+
- MySQL 8.0+
- Git

### One-Shot Setup
```bash
# Clone the repository
git clone <repository-url>
cd healthcoach-ai

# Run the complete setup script
./setup.sh

# Start the application
./run_healthcoach.sh
```

### Manual Setup
```bash
# Backend setup
cd backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Frontend setup
cd ..
npm install

# Database setup
mysql -u root -p
CREATE DATABASE healthcoach_db;
CREATE USER 'healthcoach_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON healthcoach_db.* TO 'healthcoach_user'@'localhost';

# Initialize database
cd backend
python init_db.py

# Test AI models
python test_ai_models.py
```

## 🏃‍♂️ Running the Application

### Option 1: Complete Launch Script
```bash
./run_healthcoach.sh
```

### Option 2: Manual Start
```bash
# Terminal 1: Backend
cd backend
source venv/bin/activate
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# Terminal 2: Frontend
npm run dev
```

## 🌐 Application URLs

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Interactive API**: http://localhost:8000/redoc

## 🔑 Sample Credentials

- **Patient**: `patient_demo` / `demo123`
- **Clinician**: `clinician_demo` / `demo123`

## 🏗️ Architecture

### Backend (FastAPI)
```
backend/
├── app/
│   ├── main.py              # FastAPI application
│   ├── models.py            # Database models
│   ├── schemas.py           # Pydantic schemas
│   ├── database.py          # Database configuration
│   ├── auth.py              # Authentication logic
│   ├── routers/             # API endpoints
│   │   ├── auth.py          # Authentication routes
│   │   ├── vitals.py        # Health data management
│   │   ├── ai_predictions.py # AI risk assessments
│   │   ├── wearables.py     # Device integrations
│   │   ├── gamification.py  # Achievement system
│   │   └── reports.py       # PDF generation
│   └── services/            # Business logic
│       ├── ai_service.py    # ML model integration
│       ├── recommendation_service.py # AI recommendations
│       └── wearable_service.py # Device APIs
├── requirements.txt         # Python dependencies
├── init_db.py              # Database initialization
└── test_ai_models.py       # AI model testing
```

### Frontend (React + TypeScript)
```
src/
├── components/
│   ├── ui/                  # Shadcn/ui components
│   ├── layout/              # Layout components
│   └── health/              # Health-specific components
├── pages/                   # Application pages
│   ├── PatientDashboard.tsx # Patient portal
│   └── ClinicianDashboard.tsx # Clinician interface
└── lib/                     # Utilities and configurations
```

## 🤖 AI Models

### Risk Prediction Models
- **Ensemble Model**: Combines Logistic Regression and XGBoost
- **Features**: 50+ clinical and lifestyle factors
- **Accuracy**: 87% for diabetes, 91% for hypertension
- **Explainability**: SHAP values for all predictions

### Model Files
- `final_lr_xgb_ensemble.pkl` - Pre-trained ensemble model
- Located in project root directory
- Automatically loaded by AI service

## 🔧 Configuration

### Environment Variables
Copy `backend/.env.example` to `backend/.env` and configure:

```env
# Database
DATABASE_URL=mysql+mysqlconnector://user:pass@localhost:3306/healthcoach_db

# Security
SECRET_KEY=your-secret-key
ALGORITHM=HS256

# API Keys
OPENAI_API_KEY=your-openai-key
FITBIT_CLIENT_ID=your-fitbit-id
FITBIT_CLIENT_SECRET=your-fitbit-secret

# Wearable Integrations
DEXCOM_CLIENT_ID=your-dexcom-id
GOOGLE_FIT_CLIENT_ID=your-google-fit-id
```

---

**HealthCoach AI** - Empowering better health through AI-driven insights and personalized care. 🏥✨
