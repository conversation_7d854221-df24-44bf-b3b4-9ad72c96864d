# Database Configuration
DATABASE_URL=mysql+mysqlconnector://root:@localhost:3306/healthcoach_db
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=
DB_NAME=healthcoach_db

# JWT Configuration
SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60

# CORS Configuration
ALLOWED_ORIGINS_STR=http://localhost:5173,http://localhost:3000,http://127.0.0.1:3000

# Environment
ENVIRONMENT=development

# API Keys
OPENAI_API_KEY=your-openai-api-key-here

# Wearable Integrations
FITBIT_CLIENT_ID=your-fitbit-client-id
FITBIT_CLIENT_SECRET=your-fitbit-client-secret

DEXCOM_CLIENT_ID=your-dexcom-client-id
DEXCOM_CLIENT_SECRET=your-dexcom-client-secret

GOOGLE_FIT_CLIENT_ID=your-google-fit-client-id
GOOGLE_FIT_CLIENT_SECRET=your-google-fit-client-secret

# Encryption
ENCRYPTION_KEY=your-encryption-key-for-tokens

# Backend URL (for OAuth callbacks)
BACKEND_URL=http://localhost:8000

# Email Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Logging
LOG_LEVEL=INFO

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100
