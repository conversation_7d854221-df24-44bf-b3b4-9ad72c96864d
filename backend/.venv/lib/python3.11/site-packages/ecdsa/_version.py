
# This file was generated by 'versioneer.py' (0.21) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-03-13T12:48:15+0100",
 "dirty": false,
 "error": null,
 "full-revisionid": "2a6593d840ad153a16ebdd4f9b772b290494f3e3",
 "version": "0.19.1"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
