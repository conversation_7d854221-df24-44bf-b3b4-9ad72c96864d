from pydantic_settings import BaseSettings
from typing import List
import os
from dotenv import load_dotenv

load_dotenv()

class Settings(BaseSettings):
    # Database - Using SQLite for demo (no MySQL setup required)
    database_url: str = "sqlite:///./healthcoach.db"

    # JWT
    secret_key: str = "your-super-secret-jwt-key-change-this-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30

    # CORS - Define as string and parse manually
    allowed_origins_str: str = "http://localhost:5173,http://localhost:3000"

    # Environment
    environment: str = "development"

    # API Keys
    openai_api_key: str = ""

    # Wearable Integration
    fitbit_client_id: str = ""
    fitbit_client_secret: str = ""
    dexcom_client_id: str = ""
    dexcom_client_secret: str = ""
    google_fit_client_id: str = ""
    google_fit_client_secret: str = ""

    # Encryption
    encryption_key: str = ""

    # Backend URL
    backend_url: str = "http://localhost:8000"

    class Config:
        env_file = ".env"
        env_file_encoding = 'utf-8'
        extra = 'ignore'  # Ignore extra fields from .env file

    @property
    def allowed_origins(self) -> List[str]:
        """Parse allowed origins from string"""
        return [origin.strip() for origin in self.allowed_origins_str.split(",")]

settings = Settings()
