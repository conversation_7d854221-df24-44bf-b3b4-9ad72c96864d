from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Enum, Float, Boolean, JSON, Date
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base
import enum

class UserType(enum.Enum):
    PATIENT = "patient"
    CLINICIAN = "clinician"

class RiskLevel(enum.Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    user_type = Column(Enum(UserType), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    profile = relationship("Profile", back_populates="user", uselist=False, cascade="all, delete-orphan")
    vitals = relationship("Vital", back_populates="user", cascade="all, delete-orphan")
    food_logs = relationship("FoodLog", back_populates="user", cascade="all, delete-orphan")
    symptoms = relationship("Symptom", back_populates="user", cascade="all, delete-orphan")
    risk_assessments = relationship("RiskAssessment", back_populates="user", cascade="all, delete-orphan")
    recommendations = relationship("Recommendation", back_populates="user", cascade="all, delete-orphan")
    gamification = relationship("Gamification", back_populates="user", uselist=False, cascade="all, delete-orphan")
    consents = relationship("Consent", back_populates="user", cascade="all, delete-orphan")

class Profile(Base):
    __tablename__ = "profiles"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # Personal Information
    name = Column(String(100), nullable=True)
    age = Column(Integer, nullable=True)
    gender = Column(String(20), nullable=True)
    phone = Column(String(20), nullable=True)

    # Physical Information
    height_cm = Column(Float, nullable=True)
    weight_kg = Column(Float, nullable=True)
    blood_type = Column(String(5), nullable=True)

    # Emergency Contact
    emergency_contact = Column(String(100), nullable=True)
    emergency_phone = Column(String(20), nullable=True)

    # Medical Information
    conditions = Column(JSON, nullable=True)  # List of medical conditions
    medications = Column(JSON, nullable=True)  # List of current medications
    allergies = Column(JSON, nullable=True)  # List of allergies

    # Lifestyle Information
    activity_level = Column(String(20), nullable=True)  # sedentary, light, moderate, active
    smoking_status = Column(String(20), nullable=True)  # never, former, current
    alcohol_consumption = Column(String(20), nullable=True)  # none, light, moderate, heavy

    # Additional Notes
    notes = Column(Text, nullable=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationship to user
    user = relationship("User", back_populates="profile")

class Vital(Base):
    __tablename__ = "vitals"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

    # Cardiovascular
    systolic_bp = Column(Integer, nullable=True)
    diastolic_bp = Column(Integer, nullable=True)
    heart_rate = Column(Integer, nullable=True)

    # Metabolic
    glucose_mg_dl = Column(Float, nullable=True)
    hba1c = Column(Float, nullable=True)

    # Physical Activity
    steps = Column(Integer, nullable=True)
    calories_burned = Column(Float, nullable=True)
    active_minutes = Column(Integer, nullable=True)

    # Sleep
    sleep_hours = Column(Float, nullable=True)
    sleep_quality = Column(String(20), nullable=True)  # poor, fair, good, excellent

    # Body Metrics
    weight_kg = Column(Float, nullable=True)
    body_fat_percentage = Column(Float, nullable=True)

    # Source of data
    data_source = Column(String(50), nullable=True)  # manual, fitbit, apple_health, etc.

    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship
    user = relationship("User", back_populates="vitals")

class FoodLog(Base):
    __tablename__ = "food_logs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

    # Food Information
    food_item = Column(String(255), nullable=False)
    meal_type = Column(String(20), nullable=True)  # breakfast, lunch, dinner, snack

    # Nutritional Information
    calories = Column(Float, nullable=True)
    carbs_g = Column(Float, nullable=True)
    protein_g = Column(Float, nullable=True)
    fat_g = Column(Float, nullable=True)
    fiber_g = Column(Float, nullable=True)
    sodium_mg = Column(Float, nullable=True)
    sugar_g = Column(Float, nullable=True)

    # Portion
    portion_size = Column(String(50), nullable=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship
    user = relationship("User", back_populates="food_logs")

class Symptom(Base):
    __tablename__ = "symptoms"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

    # Symptom Information
    symptom = Column(String(255), nullable=False)
    severity = Column(String(20), nullable=True)  # mild, moderate, severe
    duration = Column(String(50), nullable=True)  # minutes, hours, days

    # Context
    triggers = Column(JSON, nullable=True)  # List of potential triggers
    notes = Column(Text, nullable=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship
    user = relationship("User", back_populates="symptoms")

class RiskAssessment(Base):
    __tablename__ = "risk_assessments"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # Risk Information
    assessment_type = Column(String(50), nullable=False)  # diabetes, hypertension, cardiovascular
    risk_level = Column(Enum(RiskLevel), nullable=False)
    risk_score = Column(Float, nullable=False)  # 0.0 to 1.0
    confidence_score = Column(Float, nullable=True)  # Model confidence

    # Model Information
    model_version = Column(String(50), nullable=True)
    features_used = Column(JSON, nullable=True)  # Features used for prediction

    # SHAP Explanations
    shap_values = Column(JSON, nullable=True)  # SHAP explanation values
    top_risk_factors = Column(JSON, nullable=True)  # Top 3 risk drivers

    # Predictions
    predicted_probability = Column(Float, nullable=True)
    risk_reduction_potential = Column(Float, nullable=True)  # Potential % reduction

    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship
    user = relationship("User", back_populates="risk_assessments")

class Recommendation(Base):
    __tablename__ = "recommendations"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    risk_assessment_id = Column(Integer, ForeignKey("risk_assessments.id"), nullable=True)

    # Recommendation Information
    category = Column(String(50), nullable=False)  # diet, exercise, sleep, stress, medication
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)

    # AI Generated Content
    ai_explanation = Column(Text, nullable=True)  # GenAI generated explanation
    personalized_advice = Column(Text, nullable=True)  # Personalized coaching advice

    # Implementation
    priority = Column(String(20), nullable=True)  # low, medium, high
    difficulty = Column(String(20), nullable=True)  # easy, moderate, hard
    estimated_impact = Column(Float, nullable=True)  # Expected % risk reduction

    # Micro-goals
    micro_goals = Column(JSON, nullable=True)  # List of actionable micro-goals

    # Safety and Guidelines
    safety_checked = Column(Boolean, default=False)
    guideline_aligned = Column(Boolean, default=False)
    contraindications = Column(JSON, nullable=True)  # List of contraindications

    # Status
    is_active = Column(Boolean, default=True)
    completed = Column(Boolean, default=False)
    completed_at = Column(DateTime(timezone=True), nullable=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationship
    user = relationship("User", back_populates="recommendations")

class Gamification(Base):
    __tablename__ = "gamification"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # Points and Levels
    total_points = Column(Integer, default=0)
    level = Column(Integer, default=1)
    experience_points = Column(Integer, default=0)

    # Streaks
    current_streak = Column(Integer, default=0)
    longest_streak = Column(Integer, default=0)
    last_activity_date = Column(Date, nullable=True)

    # Badges and Achievements
    badges_earned = Column(JSON, nullable=True)  # List of badge IDs
    achievements = Column(JSON, nullable=True)  # List of achievements

    # Challenge Progress
    active_challenges = Column(JSON, nullable=True)  # Current challenges
    completed_challenges = Column(JSON, nullable=True)  # Completed challenges

    # Statistics
    goals_completed = Column(Integer, default=0)
    recommendations_followed = Column(Integer, default=0)
    data_entries = Column(Integer, default=0)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationship
    user = relationship("User", back_populates="gamification")

class Consent(Base):
    __tablename__ = "consents"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # Consent Information
    consent_type = Column(String(50), nullable=False)  # data_collection, ai_analysis, sharing
    consent_version = Column(String(20), nullable=False)
    consent_text = Column(Text, nullable=False)

    # Status
    granted = Column(Boolean, default=True)
    granted_at = Column(DateTime(timezone=True), server_default=func.now())
    revoked_at = Column(DateTime(timezone=True), nullable=True)

    # Metadata
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(512), nullable=True)

    # Relationship
    user = relationship("User", back_populates="consents")

class AuditLog(Base):
    __tablename__ = "audit_logs"

    id = Column(Integer, primary_key=True, index=True)

    # Actor Information
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    actor_type = Column(String(50), nullable=True)  # user, system, api

    # Action Information
    action = Column(String(100), nullable=False)
    resource_type = Column(String(50), nullable=True)
    resource_id = Column(String(100), nullable=True)

    # Request Information
    endpoint = Column(String(255), nullable=True)
    method = Column(String(10), nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(512), nullable=True)

    # Details
    details = Column(JSON, nullable=True)
    success = Column(Boolean, default=True)
    error_message = Column(Text, nullable=True)

    # Timing
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    duration_ms = Column(Integer, nullable=True)

class WearableIntegration(Base):
    __tablename__ = "wearable_integrations"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # Integration Information
    provider = Column(String(50), nullable=False)  # fitbit, apple_health, google_fit, dexcom
    provider_user_id = Column(String(255), nullable=True)

    # Authentication
    access_token = Column(Text, nullable=True)  # Encrypted
    refresh_token = Column(Text, nullable=True)  # Encrypted
    token_expires_at = Column(DateTime(timezone=True), nullable=True)

    # Status
    is_active = Column(Boolean, default=True)
    last_sync = Column(DateTime(timezone=True), nullable=True)
    sync_frequency = Column(String(20), default="hourly")  # hourly, daily, manual

    # Permissions
    permissions_granted = Column(JSON, nullable=True)  # List of granted permissions

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
