from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Dict, Any, List

from ..database import get_db
from ..models import User, UserType, RiskAssessment, Recommendation, Profile, Vital
from ..schemas import (
    PredictionRequest, PredictionResponse, RiskAssessmentCreate, 
    RiskAssessment as RiskAssessmentSchema, RecommendationCreate,
    Recommendation as RecommendationSchema, MessageResponse
)
from ..auth import get_current_user
from ..services.ai_service import ai_service
from ..services.recommendation_service import recommendation_service

router = APIRouter(prefix="/ai", tags=["ai-predictions"])

@router.post("/predict", response_model=PredictionResponse)
async def predict_health_risk(
    prediction_request: PredictionRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Predict health risk using AI models.
    
    - **features**: Dictionary of health features for prediction
    - **model_type**: Type of model to use (diabetes, hypertension)
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can get risk predictions"
        )
    
    try:
        # Get user profile and latest vitals for context
        profile = db.query(Profile).filter(Profile.user_id == current_user.id).first()
        latest_vital = db.query(Vital).filter(
            Vital.user_id == current_user.id
        ).order_by(Vital.timestamp.desc()).first()
        
        # Prepare user data for prediction
        user_data = prediction_request.features.copy()
        
        # Add profile data if available
        if profile:
            user_data.update({
                'age': profile.age,
                'gender': profile.gender,
                'height_cm': profile.height_cm,
                'weight_kg': profile.weight_kg,
                'activity_level': profile.activity_level,
                'smoking_status': profile.smoking_status,
                'alcohol_consumption': profile.alcohol_consumption,
                'conditions': profile.conditions or []
            })
        
        # Add latest vital signs if available
        if latest_vital:
            user_data.update({
                'systolic_bp': latest_vital.systolic_bp,
                'diastolic_bp': latest_vital.diastolic_bp,
                'heart_rate': latest_vital.heart_rate,
                'glucose_mg_dl': latest_vital.glucose_mg_dl,
                'hba1c': latest_vital.hba1c,
                'sleep_hours': latest_vital.sleep_hours
            })
        
        # Make prediction
        risk_result = ai_service.predict_risk(user_data, prediction_request.model_type)
        
        # Save risk assessment to database
        risk_assessment = RiskAssessment(
            user_id=current_user.id,
            assessment_type=prediction_request.model_type,
            risk_level=risk_result['risk_level'],
            risk_score=risk_result['risk_score'],
            confidence_score=risk_result['confidence_score'],
            model_version=risk_result['model_version'],
            features_used=risk_result['features_used'],
            top_risk_factors=risk_result['top_risk_factors'],
            predicted_probability=risk_result['predicted_probability']
        )
        
        db.add(risk_assessment)
        db.commit()
        db.refresh(risk_assessment)
        
        # Generate recommendations
        recommendations = recommendation_service.generate_recommendations(
            risk_result, user_data
        )
        
        # Save recommendations to database
        saved_recommendations = []
        for rec in recommendations:
            db_rec = Recommendation(
                user_id=current_user.id,
                risk_assessment_id=risk_assessment.id,
                category=rec.get('category', 'general'),
                title=rec.get('title', ''),
                description=rec.get('description', ''),
                ai_explanation=rec.get('ai_explanation'),
                personalized_advice=rec.get('personalized_advice'),
                priority=rec.get('priority', 'medium'),
                difficulty=rec.get('difficulty', 'moderate'),
                estimated_impact=rec.get('estimated_impact'),
                micro_goals=rec.get('micro_goals', []),
                safety_checked=rec.get('safety_checked', True),
                guideline_aligned=rec.get('guideline_aligned', True),
                contraindications=rec.get('contraindications', [])
            )
            db.add(db_rec)
            saved_recommendations.append(rec.get('title', ''))
        
        db.commit()
        
        # Generate explanation
        explanation = ai_service.get_risk_explanation(risk_result)
        
        return PredictionResponse(
            risk_level=risk_result['risk_level'],
            risk_score=risk_result['risk_score'],
            confidence_score=risk_result['confidence_score'],
            top_risk_factors=risk_result['top_risk_factors'],
            recommendations=saved_recommendations,
            explanation=explanation
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating prediction: {str(e)}"
        )

@router.get("/risk-assessments", response_model=List[RiskAssessmentSchema])
async def get_risk_assessments(
    limit: int = 10,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get user's risk assessment history.
    
    - **limit**: Maximum number of assessments to return
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can view risk assessments"
        )
    
    assessments = db.query(RiskAssessment).filter(
        RiskAssessment.user_id == current_user.id
    ).order_by(RiskAssessment.created_at.desc()).limit(limit).all()
    
    return assessments

@router.get("/risk-assessments/{assessment_id}", response_model=RiskAssessmentSchema)
async def get_risk_assessment(
    assessment_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific risk assessment.
    
    - **assessment_id**: ID of the risk assessment
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can view risk assessments"
        )
    
    assessment = db.query(RiskAssessment).filter(
        RiskAssessment.id == assessment_id,
        RiskAssessment.user_id == current_user.id
    ).first()
    
    if not assessment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Risk assessment not found"
        )
    
    return assessment

@router.get("/recommendations", response_model=List[RecommendationSchema])
async def get_recommendations(
    active_only: bool = True,
    limit: int = 20,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get user's health recommendations.
    
    - **active_only**: Only return active recommendations
    - **limit**: Maximum number of recommendations to return
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can view recommendations"
        )
    
    query = db.query(Recommendation).filter(Recommendation.user_id == current_user.id)
    
    if active_only:
        query = query.filter(Recommendation.is_active == True)
    
    recommendations = query.order_by(
        Recommendation.priority.desc(),
        Recommendation.created_at.desc()
    ).limit(limit).all()
    
    return recommendations

@router.put("/recommendations/{recommendation_id}/complete", response_model=MessageResponse)
async def complete_recommendation(
    recommendation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Mark a recommendation as completed.
    
    - **recommendation_id**: ID of the recommendation to complete
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can complete recommendations"
        )
    
    recommendation = db.query(Recommendation).filter(
        Recommendation.id == recommendation_id,
        Recommendation.user_id == current_user.id
    ).first()
    
    if not recommendation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Recommendation not found"
        )
    
    recommendation.completed = True
    recommendation.completed_at = db.func.now()
    db.commit()
    
    # Update gamification points
    from ..models import Gamification
    gamification = db.query(Gamification).filter(
        Gamification.user_id == current_user.id
    ).first()
    
    if gamification:
        # Award points based on recommendation difficulty
        points_map = {'easy': 10, 'moderate': 20, 'hard': 30}
        points = points_map.get(recommendation.difficulty, 15)
        
        gamification.total_points += points
        gamification.recommendations_followed += 1
        
        # Check for level up
        if gamification.total_points >= (gamification.level * 100):
            gamification.level += 1
        
        db.commit()
    
    return MessageResponse(
        message=f"Recommendation completed! You earned {points if 'points' in locals() else 15} points.",
        success=True
    )

@router.get("/explain/{assessment_id}")
async def explain_risk_assessment(
    assessment_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get detailed explanation of a risk assessment.
    
    - **assessment_id**: ID of the risk assessment to explain
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can view explanations"
        )
    
    assessment = db.query(RiskAssessment).filter(
        RiskAssessment.id == assessment_id,
        RiskAssessment.user_id == current_user.id
    ).first()
    
    if not assessment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Risk assessment not found"
        )
    
    # Generate detailed explanation
    explanation = {
        "assessment_type": assessment.assessment_type,
        "risk_level": assessment.risk_level,
        "risk_score": assessment.risk_score,
        "confidence": assessment.confidence_score,
        "explanation": ai_service.get_risk_explanation({
            "risk_level": assessment.risk_level,
            "risk_score": assessment.risk_score,
            "top_risk_factors": assessment.top_risk_factors
        }),
        "top_risk_factors": assessment.top_risk_factors,
        "model_info": {
            "version": assessment.model_version,
            "features_used": len(assessment.features_used or {}),
            "created_at": assessment.created_at.isoformat()
        },
        "actionable_insights": []
    }
    
    # Add actionable insights based on top risk factors
    for factor in (assessment.top_risk_factors or [])[:3]:
        feature = factor.get('feature', '')
        impact = factor.get('impact', '')
        
        if 'glucose' in feature.lower():
            explanation["actionable_insights"].append({
                "factor": "Blood Sugar",
                "current_impact": impact,
                "recommendation": "Monitor glucose levels and consider dietary changes"
            })
        elif 'bp' in feature.lower():
            explanation["actionable_insights"].append({
                "factor": "Blood Pressure",
                "current_impact": impact,
                "recommendation": "Regular monitoring and lifestyle modifications"
            })
        elif 'bmi' in feature.lower():
            explanation["actionable_insights"].append({
                "factor": "Body Weight",
                "current_impact": impact,
                "recommendation": "Focus on healthy weight management"
            })
    
    return explanation
