from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Dict, Any, List
from datetime import datetime, date

from ..database import get_db
from ..models import User, UserType, Gamification
from ..schemas import Gamification as GamificationSchema, GamificationUpdate, MessageResponse
from ..auth import get_current_user

router = APIRouter(prefix="/gamification", tags=["gamification"])

# Badge definitions
BADGES = {
    "first_steps": {
        "name": "First Steps",
        "description": "Recorded your first vital signs",
        "icon": "🎯",
        "points": 50
    },
    "week_warrior": {
        "name": "Week Warrior",
        "description": "Maintained a 7-day streak",
        "icon": "🔥",
        "points": 100
    },
    "health_hero": {
        "name": "Health Hero",
        "description": "Completed 10 recommendations",
        "icon": "🦸",
        "points": 200
    },
    "data_master": {
        "name": "Data Master",
        "description": "Logged 100 health data points",
        "icon": "📊",
        "points": 150
    },
    "consistency_king": {
        "name": "Consistency King",
        "description": "Maintained a 30-day streak",
        "icon": "👑",
        "points": 300
    },
    "goal_getter": {
        "name": "Goal Getter",
        "description": "Achieved 5 health goals",
        "icon": "🎯",
        "points": 100
    }
}

# Challenge definitions
CHALLENGES = {
    "daily_vitals": {
        "name": "Daily Vitals Challenge",
        "description": "Log your vitals every day for a week",
        "duration_days": 7,
        "target": 7,
        "reward_points": 150,
        "badge": "week_warrior"
    },
    "step_master": {
        "name": "Step Master",
        "description": "Reach 10,000 steps daily for 5 days",
        "duration_days": 7,
        "target": 5,
        "reward_points": 200,
        "badge": None
    },
    "recommendation_champion": {
        "name": "Recommendation Champion",
        "description": "Complete 5 health recommendations",
        "duration_days": 30,
        "target": 5,
        "reward_points": 250,
        "badge": "health_hero"
    }
}

@router.get("/profile", response_model=GamificationSchema)
async def get_gamification_profile(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get user's gamification profile with points, level, badges, and achievements.
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients have gamification profiles"
        )
    
    gamification = db.query(Gamification).filter(
        Gamification.user_id == current_user.id
    ).first()
    
    if not gamification:
        # Create gamification profile if it doesn't exist
        gamification = Gamification(user_id=current_user.id)
        db.add(gamification)
        db.commit()
        db.refresh(gamification)
    
    return gamification

@router.get("/leaderboard")
async def get_leaderboard(
    limit: int = 10,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get leaderboard showing top users by points.
    
    - **limit**: Number of top users to return (max 50)
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can view leaderboard"
        )
    
    limit = min(limit, 50)  # Cap at 50
    
    # Get top users by points
    top_users = db.query(Gamification, User).join(
        User, Gamification.user_id == User.id
    ).filter(
        User.user_type == UserType.PATIENT
    ).order_by(
        Gamification.total_points.desc()
    ).limit(limit).all()
    
    leaderboard = []
    for rank, (gamification, user) in enumerate(top_users, 1):
        # Get user's profile for display name
        profile = user.profile
        display_name = profile.name if profile and profile.name else user.username
        
        leaderboard.append({
            "rank": rank,
            "user_id": user.id,
            "display_name": display_name,
            "total_points": gamification.total_points,
            "level": gamification.level,
            "badges_count": len(gamification.badges_earned or []),
            "current_streak": gamification.current_streak,
            "is_current_user": user.id == current_user.id
        })
    
    # Find current user's rank if not in top list
    current_user_rank = None
    if not any(entry["is_current_user"] for entry in leaderboard):
        user_gamification = db.query(Gamification).filter(
            Gamification.user_id == current_user.id
        ).first()
        
        if user_gamification:
            higher_users = db.query(Gamification).filter(
                Gamification.total_points > user_gamification.total_points
            ).count()
            current_user_rank = higher_users + 1
    
    return {
        "leaderboard": leaderboard,
        "current_user_rank": current_user_rank,
        "total_users": db.query(Gamification).count()
    }

@router.get("/badges")
async def get_available_badges():
    """
    Get all available badges and their requirements.
    """
    return {
        "badges": BADGES
    }

@router.get("/challenges")
async def get_available_challenges(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get available challenges and user's progress.
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can view challenges"
        )
    
    gamification = db.query(Gamification).filter(
        Gamification.user_id == current_user.id
    ).first()
    
    active_challenges = gamification.active_challenges or [] if gamification else []
    completed_challenges = gamification.completed_challenges or [] if gamification else []
    
    challenges_with_progress = []
    for challenge_id, challenge in CHALLENGES.items():
        # Check if challenge is active or completed
        active_challenge = next((c for c in active_challenges if c.get("id") == challenge_id), None)
        is_completed = any(c.get("id") == challenge_id for c in completed_challenges)
        
        challenge_data = challenge.copy()
        challenge_data["id"] = challenge_id
        challenge_data["is_active"] = bool(active_challenge)
        challenge_data["is_completed"] = is_completed
        challenge_data["progress"] = active_challenge.get("progress", 0) if active_challenge else 0
        challenge_data["started_at"] = active_challenge.get("started_at") if active_challenge else None
        
        challenges_with_progress.append(challenge_data)
    
    return {
        "challenges": challenges_with_progress
    }

@router.post("/challenges/{challenge_id}/start", response_model=MessageResponse)
async def start_challenge(
    challenge_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Start a new challenge.
    
    - **challenge_id**: ID of the challenge to start
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can start challenges"
        )
    
    if challenge_id not in CHALLENGES:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Challenge not found"
        )
    
    gamification = db.query(Gamification).filter(
        Gamification.user_id == current_user.id
    ).first()
    
    if not gamification:
        gamification = Gamification(user_id=current_user.id)
        db.add(gamification)
        db.commit()
        db.refresh(gamification)
    
    # Check if challenge is already active or completed
    active_challenges = gamification.active_challenges or []
    completed_challenges = gamification.completed_challenges or []
    
    if any(c.get("id") == challenge_id for c in active_challenges):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Challenge is already active"
        )
    
    if any(c.get("id") == challenge_id for c in completed_challenges):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Challenge already completed"
        )
    
    # Start the challenge
    new_challenge = {
        "id": challenge_id,
        "started_at": datetime.utcnow().isoformat(),
        "progress": 0,
        "target": CHALLENGES[challenge_id]["target"]
    }
    
    active_challenges.append(new_challenge)
    gamification.active_challenges = active_challenges
    
    db.commit()
    
    return MessageResponse(
        message=f"Challenge '{CHALLENGES[challenge_id]['name']}' started!",
        success=True
    )

@router.post("/update", response_model=MessageResponse)
async def update_gamification(
    update_data: GamificationUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update gamification stats (used internally by other endpoints).
    
    - **points_to_add**: Points to add to user's total
    - **new_badge**: Badge ID to award
    - **completed_goal**: Whether user completed a goal
    - **data_entry**: Whether user made a data entry
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients have gamification profiles"
        )
    
    gamification = db.query(Gamification).filter(
        Gamification.user_id == current_user.id
    ).first()
    
    if not gamification:
        gamification = Gamification(user_id=current_user.id)
        db.add(gamification)
        db.commit()
        db.refresh(gamification)
    
    messages = []
    
    # Add points
    if update_data.points_to_add:
        gamification.total_points += update_data.points_to_add
        gamification.experience_points += update_data.points_to_add
        messages.append(f"Earned {update_data.points_to_add} points!")
        
        # Check for level up
        points_for_next_level = gamification.level * 100
        if gamification.experience_points >= points_for_next_level:
            gamification.level += 1
            gamification.experience_points = 0
            messages.append(f"Level up! You're now level {gamification.level}!")
    
    # Award badge
    if update_data.new_badge:
        badges_earned = gamification.badges_earned or []
        if update_data.new_badge not in badges_earned:
            badges_earned.append(update_data.new_badge)
            gamification.badges_earned = badges_earned
            
            badge_info = BADGES.get(update_data.new_badge, {})
            badge_name = badge_info.get("name", update_data.new_badge)
            badge_points = badge_info.get("points", 0)
            
            if badge_points:
                gamification.total_points += badge_points
            
            messages.append(f"New badge earned: {badge_name}!")
    
    # Update counters
    if update_data.completed_goal:
        gamification.goals_completed += 1
        
        # Check for goal-related badges
        if gamification.goals_completed == 5 and "goal_getter" not in (gamification.badges_earned or []):
            badges_earned = gamification.badges_earned or []
            badges_earned.append("goal_getter")
            gamification.badges_earned = badges_earned
            gamification.total_points += BADGES["goal_getter"]["points"]
            messages.append("New badge earned: Goal Getter!")
    
    if update_data.data_entry:
        gamification.data_entries += 1
        
        # Update streak
        today = date.today()
        if gamification.last_activity_date == today:
            # Already logged today, no streak update
            pass
        elif gamification.last_activity_date == today - timedelta(days=1):
            # Consecutive day, increment streak
            gamification.current_streak += 1
            if gamification.current_streak > gamification.longest_streak:
                gamification.longest_streak = gamification.current_streak
        else:
            # Streak broken, reset
            gamification.current_streak = 1
        
        gamification.last_activity_date = today
        
        # Check for streak badges
        if gamification.current_streak == 7 and "week_warrior" not in (gamification.badges_earned or []):
            badges_earned = gamification.badges_earned or []
            badges_earned.append("week_warrior")
            gamification.badges_earned = badges_earned
            gamification.total_points += BADGES["week_warrior"]["points"]
            messages.append("New badge earned: Week Warrior!")
        
        # Check for data master badge
        if gamification.data_entries == 100 and "data_master" not in (gamification.badges_earned or []):
            badges_earned = gamification.badges_earned or []
            badges_earned.append("data_master")
            gamification.badges_earned = badges_earned
            gamification.total_points += BADGES["data_master"]["points"]
            messages.append("New badge earned: Data Master!")
    
    db.commit()
    
    return MessageResponse(
        message=" ".join(messages) if messages else "Gamification updated!",
        success=True
    )
