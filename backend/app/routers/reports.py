from fastapi import APIRouter, Depends, HTTPException, status, Response
from sqlalchemy.orm import Session
from typing import Optional
from datetime import datetime, timedelta
import io
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import base64

from ..database import get_db
from ..models import User, UserType, Profile, Vital, RiskAssessment, Recommendation, Gamification
from ..auth import get_current_user

router = APIRouter(prefix="/reports", tags=["reports"])

@router.get("/wellness-report")
async def generate_wellness_report(
    days: int = 30,
    format: str = "pdf",
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Generate a comprehensive wellness report.
    
    - **days**: Number of days to include in the report (default: 30)
    - **format**: Report format (pdf, json)
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can generate wellness reports"
        )
    
    # Gather data for the report
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)
    
    # Get user profile
    profile = db.query(Profile).filter(Profile.user_id == current_user.id).first()
    
    # Get vitals data
    vitals = db.query(Vital).filter(
        Vital.user_id == current_user.id,
        Vital.timestamp >= start_date
    ).order_by(Vital.timestamp.desc()).all()
    
    # Get risk assessments
    risk_assessments = db.query(RiskAssessment).filter(
        RiskAssessment.user_id == current_user.id,
        RiskAssessment.created_at >= start_date
    ).order_by(RiskAssessment.created_at.desc()).all()
    
    # Get recommendations
    recommendations = db.query(Recommendation).filter(
        Recommendation.user_id == current_user.id,
        Recommendation.created_at >= start_date
    ).order_by(Recommendation.created_at.desc()).all()
    
    # Get gamification data
    gamification = db.query(Gamification).filter(
        Gamification.user_id == current_user.id
    ).first()
    
    if format == "json":
        return _generate_json_report(
            current_user, profile, vitals, risk_assessments, 
            recommendations, gamification, days
        )
    else:
        # Generate PDF report
        pdf_buffer = _generate_pdf_report(
            current_user, profile, vitals, risk_assessments,
            recommendations, gamification, days
        )
        
        return Response(
            content=pdf_buffer.getvalue(),
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename=wellness_report_{current_user.username}_{datetime.now().strftime('%Y%m%d')}.pdf"
            }
        )

def _generate_json_report(user, profile, vitals, risk_assessments, recommendations, gamification, days):
    """Generate JSON format wellness report"""
    
    # Calculate vital statistics
    vital_stats = {}
    if vitals:
        # Blood pressure stats
        bp_readings = [(v.systolic_bp, v.diastolic_bp) for v in vitals if v.systolic_bp and v.diastolic_bp]
        if bp_readings:
            systolic_values = [bp[0] for bp in bp_readings]
            diastolic_values = [bp[1] for bp in bp_readings]
            vital_stats["blood_pressure"] = {
                "average_systolic": round(sum(systolic_values) / len(systolic_values), 1),
                "average_diastolic": round(sum(diastolic_values) / len(diastolic_values), 1),
                "readings_count": len(bp_readings)
            }
        
        # Heart rate stats
        hr_readings = [v.heart_rate for v in vitals if v.heart_rate]
        if hr_readings:
            vital_stats["heart_rate"] = {
                "average": round(sum(hr_readings) / len(hr_readings), 1),
                "min": min(hr_readings),
                "max": max(hr_readings),
                "readings_count": len(hr_readings)
            }
        
        # Glucose stats
        glucose_readings = [v.glucose_mg_dl for v in vitals if v.glucose_mg_dl]
        if glucose_readings:
            vital_stats["glucose"] = {
                "average": round(sum(glucose_readings) / len(glucose_readings), 1),
                "min": min(glucose_readings),
                "max": max(glucose_readings),
                "readings_count": len(glucose_readings)
            }
    
    # Risk assessment summary
    risk_summary = {}
    if risk_assessments:
        latest_risk = risk_assessments[0]
        risk_summary = {
            "latest_assessment": {
                "type": latest_risk.assessment_type,
                "risk_level": latest_risk.risk_level,
                "risk_score": latest_risk.risk_score,
                "date": latest_risk.created_at.isoformat(),
                "top_risk_factors": latest_risk.top_risk_factors
            },
            "total_assessments": len(risk_assessments)
        }
    
    # Recommendations summary
    rec_summary = {
        "total_recommendations": len(recommendations),
        "completed": len([r for r in recommendations if r.completed]),
        "active": len([r for r in recommendations if r.is_active and not r.completed]),
        "categories": {}
    }
    
    # Group recommendations by category
    for rec in recommendations:
        category = rec.category
        if category not in rec_summary["categories"]:
            rec_summary["categories"][category] = {"total": 0, "completed": 0}
        rec_summary["categories"][category]["total"] += 1
        if rec.completed:
            rec_summary["categories"][category]["completed"] += 1
    
    return {
        "report_info": {
            "generated_at": datetime.utcnow().isoformat(),
            "period_days": days,
            "user_id": user.id,
            "username": user.username
        },
        "user_profile": {
            "name": profile.name if profile else None,
            "age": profile.age if profile else None,
            "gender": profile.gender if profile else None
        },
        "vital_statistics": vital_stats,
        "risk_assessment": risk_summary,
        "recommendations": rec_summary,
        "gamification": {
            "total_points": gamification.total_points if gamification else 0,
            "level": gamification.level if gamification else 1,
            "current_streak": gamification.current_streak if gamification else 0,
            "badges_earned": len(gamification.badges_earned or []) if gamification else 0
        }
    }

def _generate_pdf_report(user, profile, vitals, risk_assessments, recommendations, gamification, days):
    """Generate PDF format wellness report"""
    
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)
    
    # Container for the 'Flowable' objects
    elements = []
    
    # Define styles
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.HexColor('#2E86AB')
    )
    
    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=16,
        spaceAfter=12,
        textColor=colors.HexColor('#A23B72')
    )
    
    # Title
    elements.append(Paragraph("HealthCoach AI - Wellness Report", title_style))
    elements.append(Spacer(1, 12))
    
    # User info
    user_name = profile.name if profile and profile.name else user.username
    elements.append(Paragraph(f"Patient: {user_name}", styles['Normal']))
    elements.append(Paragraph(f"Report Period: {days} days", styles['Normal']))
    elements.append(Paragraph(f"Generated: {datetime.now().strftime('%B %d, %Y')}", styles['Normal']))
    elements.append(Spacer(1, 20))
    
    # Executive Summary
    elements.append(Paragraph("Executive Summary", heading_style))
    
    summary_text = f"This report covers your health data for the past {days} days. "
    if vitals:
        summary_text += f"You recorded {len(vitals)} vital sign entries. "
    if risk_assessments:
        latest_risk = risk_assessments[0]
        summary_text += f"Your latest risk assessment shows {latest_risk.risk_level} risk for {latest_risk.assessment_type}. "
    if recommendations:
        completed_recs = len([r for r in recommendations if r.completed])
        summary_text += f"You have completed {completed_recs} out of {len(recommendations)} recommendations. "
    
    elements.append(Paragraph(summary_text, styles['Normal']))
    elements.append(Spacer(1, 20))
    
    # Vital Signs Summary
    if vitals:
        elements.append(Paragraph("Vital Signs Summary", heading_style))
        
        # Create vital signs table
        vital_data = [['Metric', 'Average', 'Range', 'Readings']]
        
        # Blood pressure
        bp_readings = [(v.systolic_bp, v.diastolic_bp) for v in vitals if v.systolic_bp and v.diastolic_bp]
        if bp_readings:
            systolic_values = [bp[0] for bp in bp_readings]
            diastolic_values = [bp[1] for bp in bp_readings]
            avg_sys = round(sum(systolic_values) / len(systolic_values), 1)
            avg_dia = round(sum(diastolic_values) / len(diastolic_values), 1)
            vital_data.append([
                'Blood Pressure (mmHg)',
                f'{avg_sys}/{avg_dia}',
                f'{min(systolic_values)}-{max(systolic_values)}/{min(diastolic_values)}-{max(diastolic_values)}',
                str(len(bp_readings))
            ])
        
        # Heart rate
        hr_readings = [v.heart_rate for v in vitals if v.heart_rate]
        if hr_readings:
            vital_data.append([
                'Heart Rate (bpm)',
                str(round(sum(hr_readings) / len(hr_readings), 1)),
                f'{min(hr_readings)}-{max(hr_readings)}',
                str(len(hr_readings))
            ])
        
        # Glucose
        glucose_readings = [v.glucose_mg_dl for v in vitals if v.glucose_mg_dl]
        if glucose_readings:
            vital_data.append([
                'Glucose (mg/dL)',
                str(round(sum(glucose_readings) / len(glucose_readings), 1)),
                f'{min(glucose_readings)}-{max(glucose_readings)}',
                str(len(glucose_readings))
            ])
        
        if len(vital_data) > 1:
            vital_table = Table(vital_data)
            vital_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            elements.append(vital_table)
            elements.append(Spacer(1, 20))
    
    # Risk Assessment
    if risk_assessments:
        elements.append(Paragraph("Risk Assessment", heading_style))
        latest_risk = risk_assessments[0]
        
        risk_text = f"Your latest {latest_risk.assessment_type} risk assessment shows a "
        risk_text += f"{latest_risk.risk_level.upper()} risk level with a score of {latest_risk.risk_score:.2%}. "
        
        if latest_risk.top_risk_factors:
            risk_text += "Key risk factors include: "
            factors = [f["feature"] for f in latest_risk.top_risk_factors[:3]]
            risk_text += ", ".join(factors) + ". "
        
        elements.append(Paragraph(risk_text, styles['Normal']))
        elements.append(Spacer(1, 20))
    
    # Recommendations
    if recommendations:
        elements.append(Paragraph("Health Recommendations", heading_style))
        
        completed_recs = [r for r in recommendations if r.completed]
        active_recs = [r for r in recommendations if r.is_active and not r.completed]
        
        elements.append(Paragraph(f"Completed Recommendations: {len(completed_recs)}", styles['Normal']))
        elements.append(Paragraph(f"Active Recommendations: {len(active_recs)}", styles['Normal']))
        
        if active_recs:
            elements.append(Paragraph("Current Active Recommendations:", styles['Normal']))
            for i, rec in enumerate(active_recs[:5], 1):  # Show top 5
                elements.append(Paragraph(f"{i}. {rec.title} ({rec.priority} priority)", styles['Normal']))
        
        elements.append(Spacer(1, 20))
    
    # Gamification Summary
    if gamification:
        elements.append(Paragraph("Achievement Summary", heading_style))
        
        achievement_text = f"You have earned {gamification.total_points} points and reached level {gamification.level}. "
        achievement_text += f"Your current streak is {gamification.current_streak} days "
        achievement_text += f"(longest: {gamification.longest_streak} days). "
        
        badges_count = len(gamification.badges_earned or [])
        achievement_text += f"You have earned {badges_count} badges and completed {gamification.goals_completed} goals."
        
        elements.append(Paragraph(achievement_text, styles['Normal']))
        elements.append(Spacer(1, 20))
    
    # Footer
    elements.append(Spacer(1, 30))
    elements.append(Paragraph("This report was generated by HealthCoach AI. Continue your health journey!", styles['Italic']))
    
    # Build PDF
    doc.build(elements)
    buffer.seek(0)
    
    return buffer

@router.get("/summary")
async def get_health_summary(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a quick health summary for dashboard display.
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can view health summary"
        )
    
    # Get latest data
    latest_vital = db.query(Vital).filter(
        Vital.user_id == current_user.id
    ).order_by(Vital.timestamp.desc()).first()
    
    latest_risk = db.query(RiskAssessment).filter(
        RiskAssessment.user_id == current_user.id
    ).order_by(RiskAssessment.created_at.desc()).first()
    
    active_recommendations = db.query(Recommendation).filter(
        Recommendation.user_id == current_user.id,
        Recommendation.is_active == True,
        Recommendation.completed == False
    ).count()
    
    gamification = db.query(Gamification).filter(
        Gamification.user_id == current_user.id
    ).first()
    
    return {
        "latest_vital": {
            "timestamp": latest_vital.timestamp.isoformat() if latest_vital else None,
            "systolic_bp": latest_vital.systolic_bp if latest_vital else None,
            "diastolic_bp": latest_vital.diastolic_bp if latest_vital else None,
            "heart_rate": latest_vital.heart_rate if latest_vital else None,
            "glucose": latest_vital.glucose_mg_dl if latest_vital else None
        } if latest_vital else None,
        "latest_risk_assessment": {
            "type": latest_risk.assessment_type,
            "level": latest_risk.risk_level,
            "score": latest_risk.risk_score,
            "date": latest_risk.created_at.isoformat()
        } if latest_risk else None,
        "active_recommendations": active_recommendations,
        "gamification": {
            "points": gamification.total_points if gamification else 0,
            "level": gamification.level if gamification else 1,
            "streak": gamification.current_streak if gamification else 0
        }
    }
