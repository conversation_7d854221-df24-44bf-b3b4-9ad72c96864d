from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta

from ..database import get_db
from ..models import User, Vital, UserType
from ..schemas import VitalCreate, Vital as VitalSchema, MessageResponse
from ..auth import get_current_user

router = APIRouter(prefix="/vitals", tags=["vitals"])

@router.post("/", response_model=MessageResponse)
async def create_vital(
    vital_data: VitalCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new vital signs record.
    
    - **systolic_bp**: Systolic blood pressure (mmHg)
    - **diastolic_bp**: Diastolic blood pressure (mmHg)
    - **heart_rate**: Heart rate (bpm)
    - **glucose_mg_dl**: Blood glucose level (mg/dL)
    - **hba1c**: HbA1c percentage
    - **steps**: Daily step count
    - **calories_burned**: Calories burned
    - **active_minutes**: Active minutes
    - **sleep_hours**: Hours of sleep
    - **sleep_quality**: Sleep quality (poor, fair, good, excellent)
    - **weight_kg**: Weight in kilograms
    - **body_fat_percentage**: Body fat percentage
    - **data_source**: Source of the data (manual, fitbit, etc.)
    """
    # Only patients can create vitals for themselves
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can record vital signs"
        )
    
    # Create vital record
    db_vital = Vital(
        user_id=current_user.id,
        timestamp=vital_data.timestamp or datetime.utcnow(),
        systolic_bp=vital_data.systolic_bp,
        diastolic_bp=vital_data.diastolic_bp,
        heart_rate=vital_data.heart_rate,
        glucose_mg_dl=vital_data.glucose_mg_dl,
        hba1c=vital_data.hba1c,
        steps=vital_data.steps,
        calories_burned=vital_data.calories_burned,
        active_minutes=vital_data.active_minutes,
        sleep_hours=vital_data.sleep_hours,
        sleep_quality=vital_data.sleep_quality,
        weight_kg=vital_data.weight_kg,
        body_fat_percentage=vital_data.body_fat_percentage,
        data_source=vital_data.data_source or "manual"
    )
    
    db.add(db_vital)
    db.commit()
    db.refresh(db_vital)
    
    return MessageResponse(
        message="Vital signs recorded successfully!",
        success=True
    )

@router.get("/", response_model=List[VitalSchema])
async def get_vitals(
    limit: int = Query(default=50, le=200),
    offset: int = Query(default=0, ge=0),
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get vital signs records for the current user.
    
    - **limit**: Maximum number of records to return (max 200)
    - **offset**: Number of records to skip
    - **start_date**: Filter records from this date
    - **end_date**: Filter records until this date
    """
    # Only patients can view their own vitals
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can view vital signs"
        )
    
    query = db.query(Vital).filter(Vital.user_id == current_user.id)
    
    # Apply date filters
    if start_date:
        query = query.filter(Vital.timestamp >= start_date)
    if end_date:
        query = query.filter(Vital.timestamp <= end_date)
    
    # Order by timestamp descending and apply pagination
    vitals = query.order_by(Vital.timestamp.desc()).offset(offset).limit(limit).all()
    
    return vitals

@router.get("/latest", response_model=Optional[VitalSchema])
async def get_latest_vital(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get the most recent vital signs record for the current user.
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can view vital signs"
        )
    
    latest_vital = db.query(Vital).filter(
        Vital.user_id == current_user.id
    ).order_by(Vital.timestamp.desc()).first()
    
    return latest_vital

@router.get("/summary")
async def get_vitals_summary(
    days: int = Query(default=30, ge=1, le=365),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a summary of vital signs over the specified number of days.
    
    - **days**: Number of days to include in summary (1-365)
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can view vital signs"
        )
    
    # Get vitals from the last N days
    start_date = datetime.utcnow() - timedelta(days=days)
    vitals = db.query(Vital).filter(
        Vital.user_id == current_user.id,
        Vital.timestamp >= start_date
    ).all()
    
    if not vitals:
        return {
            "period_days": days,
            "total_records": 0,
            "message": "No vital signs recorded in this period"
        }
    
    # Calculate averages and ranges
    summary = {
        "period_days": days,
        "total_records": len(vitals),
        "date_range": {
            "start": min(v.timestamp for v in vitals).isoformat(),
            "end": max(v.timestamp for v in vitals).isoformat()
        }
    }
    
    # Blood pressure summary
    bp_readings = [(v.systolic_bp, v.diastolic_bp) for v in vitals 
                   if v.systolic_bp and v.diastolic_bp]
    if bp_readings:
        systolic_values = [bp[0] for bp in bp_readings]
        diastolic_values = [bp[1] for bp in bp_readings]
        summary["blood_pressure"] = {
            "readings_count": len(bp_readings),
            "systolic": {
                "average": round(sum(systolic_values) / len(systolic_values), 1),
                "min": min(systolic_values),
                "max": max(systolic_values)
            },
            "diastolic": {
                "average": round(sum(diastolic_values) / len(diastolic_values), 1),
                "min": min(diastolic_values),
                "max": max(diastolic_values)
            }
        }
    
    # Heart rate summary
    hr_readings = [v.heart_rate for v in vitals if v.heart_rate]
    if hr_readings:
        summary["heart_rate"] = {
            "readings_count": len(hr_readings),
            "average": round(sum(hr_readings) / len(hr_readings), 1),
            "min": min(hr_readings),
            "max": max(hr_readings)
        }
    
    # Glucose summary
    glucose_readings = [v.glucose_mg_dl for v in vitals if v.glucose_mg_dl]
    if glucose_readings:
        summary["glucose"] = {
            "readings_count": len(glucose_readings),
            "average": round(sum(glucose_readings) / len(glucose_readings), 1),
            "min": min(glucose_readings),
            "max": max(glucose_readings)
        }
    
    # Activity summary
    step_readings = [v.steps for v in vitals if v.steps]
    if step_readings:
        summary["activity"] = {
            "step_readings_count": len(step_readings),
            "average_daily_steps": round(sum(step_readings) / len(step_readings)),
            "total_steps": sum(step_readings),
            "max_daily_steps": max(step_readings)
        }
    
    # Sleep summary
    sleep_readings = [v.sleep_hours for v in vitals if v.sleep_hours]
    if sleep_readings:
        summary["sleep"] = {
            "readings_count": len(sleep_readings),
            "average_hours": round(sum(sleep_readings) / len(sleep_readings), 1),
            "min_hours": min(sleep_readings),
            "max_hours": max(sleep_readings)
        }
    
    # Weight summary
    weight_readings = [v.weight_kg for v in vitals if v.weight_kg]
    if weight_readings:
        summary["weight"] = {
            "readings_count": len(weight_readings),
            "current": weight_readings[0] if vitals else None,  # Most recent
            "average": round(sum(weight_readings) / len(weight_readings), 1),
            "min": min(weight_readings),
            "max": max(weight_readings),
            "change": round(weight_readings[0] - weight_readings[-1], 1) if len(weight_readings) > 1 else 0
        }
    
    return summary

@router.delete("/{vital_id}", response_model=MessageResponse)
async def delete_vital(
    vital_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a specific vital signs record.
    
    - **vital_id**: ID of the vital record to delete
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can delete vital signs"
        )
    
    # Find the vital record
    vital = db.query(Vital).filter(
        Vital.id == vital_id,
        Vital.user_id == current_user.id
    ).first()
    
    if not vital:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Vital signs record not found"
        )
    
    db.delete(vital)
    db.commit()
    
    return MessageResponse(
        message="Vital signs record deleted successfully",
        success=True
    )
