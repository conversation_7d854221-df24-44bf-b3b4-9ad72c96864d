from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

from ..database import get_db
from ..models import User, UserType, WearableIntegration, Vital
from ..schemas import MessageResponse
from ..auth import get_current_user
from ..services.wearable_service import wearable_service

router = APIRouter(prefix="/wearables", tags=["wearable-integrations"])

@router.get("/providers")
async def get_supported_providers():
    """
    Get list of supported wearable providers.
    """
    return {
        "providers": [
            {
                "id": "fitbit",
                "name": "Fitbit",
                "description": "Sync activity, heart rate, and sleep data",
                "data_types": ["steps", "heart_rate", "sleep", "calories"],
                "status": "available"
            },
            {
                "id": "dexcom",
                "name": "Dexcom CGM",
                "description": "Continuous glucose monitoring data",
                "data_types": ["glucose"],
                "status": "available"
            },
            {
                "id": "google_fit",
                "name": "Google Fit",
                "description": "Activity and health data from Google Fit",
                "data_types": ["steps", "heart_rate", "activity"],
                "status": "available"
            },
            {
                "id": "apple_health",
                "name": "Apple HealthKit",
                "description": "Comprehensive health data from iOS devices",
                "data_types": ["steps", "heart_rate", "sleep", "glucose", "blood_pressure"],
                "status": "coming_soon"
            }
        ]
    }

@router.get("/integrations", response_model=List[dict])
async def get_user_integrations(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get user's current wearable integrations.
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can manage wearable integrations"
        )
    
    integrations = db.query(WearableIntegration).filter(
        WearableIntegration.user_id == current_user.id
    ).all()
    
    result = []
    for integration in integrations:
        result.append({
            "id": integration.id,
            "provider": integration.provider,
            "is_active": integration.is_active,
            "last_sync": integration.last_sync.isoformat() if integration.last_sync else None,
            "sync_frequency": integration.sync_frequency,
            "permissions_granted": integration.permissions_granted,
            "created_at": integration.created_at.isoformat()
        })
    
    return result

@router.post("/connect/{provider}")
async def connect_wearable(
    provider: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Initiate connection to a wearable provider.
    
    - **provider**: Provider ID (fitbit, dexcom, google_fit, apple_health)
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can connect wearable devices"
        )
    
    supported_providers = ["fitbit", "dexcom", "google_fit"]
    if provider not in supported_providers:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Provider '{provider}' is not supported. Supported providers: {supported_providers}"
        )
    
    # Check if integration already exists
    existing_integration = db.query(WearableIntegration).filter(
        WearableIntegration.user_id == current_user.id,
        WearableIntegration.provider == provider
    ).first()
    
    if existing_integration and existing_integration.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Already connected to {provider}"
        )
    
    try:
        # Get authorization URL
        auth_data = wearable_service.get_authorization_url(provider, current_user.id)
        
        return {
            "authorization_url": auth_data["authorization_url"],
            "provider": provider,
            "instructions": f"Please visit the authorization URL to connect your {provider} account. You will be redirected back to complete the setup."
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error initiating connection: {str(e)}"
        )

@router.get("/callback/{provider}")
async def wearable_callback(
    provider: str,
    code: str,
    state: str,
    db: Session = Depends(get_db)
):
    """
    Handle OAuth callback from wearable provider.
    
    - **provider**: Provider ID
    - **code**: Authorization code from provider
    - **state**: State parameter containing user ID
    """
    try:
        user_id = int(state)
        
        # Verify user exists
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Exchange code for token
        token_data = await wearable_service.exchange_code_for_token(provider, code, user_id)
        
        # Save or update integration
        integration = db.query(WearableIntegration).filter(
            WearableIntegration.user_id == user_id,
            WearableIntegration.provider == provider
        ).first()
        
        if integration:
            # Update existing integration
            integration.access_token = token_data["access_token"]
            integration.refresh_token = token_data["refresh_token"]
            integration.token_expires_at = datetime.utcnow() + timedelta(seconds=token_data.get("expires_in", 3600))
            integration.is_active = True
        else:
            # Create new integration
            integration = WearableIntegration(
                user_id=user_id,
                provider=provider,
                access_token=token_data["access_token"],
                refresh_token=token_data["refresh_token"],
                token_expires_at=datetime.utcnow() + timedelta(seconds=token_data.get("expires_in", 3600)),
                is_active=True,
                sync_frequency="hourly"
            )
            db.add(integration)
        
        db.commit()
        
        return {
            "message": f"Successfully connected to {provider}!",
            "provider": provider,
            "status": "connected",
            "next_sync": "Data will be synced automatically based on your preferences."
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error completing connection: {str(e)}"
        )

@router.post("/sync/{provider}", response_model=MessageResponse)
async def sync_wearable_data(
    provider: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Manually sync data from a wearable provider.
    
    - **provider**: Provider ID to sync data from
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can sync wearable data"
        )
    
    # Find the integration
    integration = db.query(WearableIntegration).filter(
        WearableIntegration.user_id == current_user.id,
        WearableIntegration.provider == provider,
        WearableIntegration.is_active == True
    ).first()
    
    if not integration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"No active {provider} integration found"
        )
    
    try:
        # Sync data from provider
        vitals_data = await wearable_service.sync_provider_data(provider, {
            "access_token": integration.access_token,
            "refresh_token": integration.refresh_token
        })
        
        # Save vitals to database
        records_created = 0
        for vital_data in vitals_data:
            db_vital = Vital(
                user_id=current_user.id,
                timestamp=vital_data.get('timestamp', datetime.utcnow()),
                systolic_bp=vital_data.get('systolic_bp'),
                diastolic_bp=vital_data.get('diastolic_bp'),
                heart_rate=vital_data.get('heart_rate'),
                glucose_mg_dl=vital_data.get('glucose_mg_dl'),
                steps=vital_data.get('steps'),
                calories_burned=vital_data.get('calories_burned'),
                sleep_hours=vital_data.get('sleep_hours'),
                sleep_quality=vital_data.get('sleep_quality'),
                weight_kg=vital_data.get('weight_kg'),
                data_source=vital_data.get('data_source', provider)
            )
            db.add(db_vital)
            records_created += 1
        
        # Update last sync time
        integration.last_sync = datetime.utcnow()
        
        db.commit()
        
        return MessageResponse(
            message=f"Successfully synced {records_created} records from {provider}",
            success=True
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error syncing data: {str(e)}"
        )

@router.delete("/integrations/{integration_id}", response_model=MessageResponse)
async def disconnect_wearable(
    integration_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Disconnect a wearable integration.
    
    - **integration_id**: ID of the integration to disconnect
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can disconnect wearable integrations"
        )
    
    integration = db.query(WearableIntegration).filter(
        WearableIntegration.id == integration_id,
        WearableIntegration.user_id == current_user.id
    ).first()
    
    if not integration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Integration not found"
        )
    
    # Deactivate integration (don't delete to preserve history)
    integration.is_active = False
    db.commit()
    
    return MessageResponse(
        message=f"Successfully disconnected from {integration.provider}",
        success=True
    )

@router.put("/integrations/{integration_id}/settings", response_model=MessageResponse)
async def update_integration_settings(
    integration_id: int,
    sync_frequency: str = Query(..., regex="^(manual|hourly|daily)$"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update wearable integration settings.
    
    - **integration_id**: ID of the integration to update
    - **sync_frequency**: How often to sync data (manual, hourly, daily)
    """
    if current_user.user_type != UserType.PATIENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only patients can update integration settings"
        )
    
    integration = db.query(WearableIntegration).filter(
        WearableIntegration.id == integration_id,
        WearableIntegration.user_id == current_user.id
    ).first()
    
    if not integration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Integration not found"
        )
    
    integration.sync_frequency = sync_frequency
    db.commit()
    
    return MessageResponse(
        message=f"Integration settings updated. Sync frequency set to {sync_frequency}",
        success=True
    )
