from pydantic import BaseModel, EmailStr, field_validator, Field
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from enum import Enum

class UserType(str, Enum):
    PATIENT = "patient"
    CLINICIAN = "clinician"

class RiskLevel(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"

# User Schemas
class UserBase(BaseModel):
    username: str
    email: EmailStr
    user_type: UserType

class UserCreate(UserBase):
    password: str

    @field_validator('password')
    @classmethod
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        return v

class UserLogin(BaseModel):
    email: str  # Can be email or username
    password: str

class User(UserBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}

# Profile Schemas
class MedicationItem(BaseModel):
    name: str
    dosage: str
    frequency: str

class ProfileBase(BaseModel):
    name: Optional[str] = None
    age: Optional[int] = None
    gender: Optional[str] = None
    phone: Optional[str] = None
    height_cm: Optional[float] = None
    weight_kg: Optional[float] = None
    blood_type: Optional[str] = None
    emergency_contact: Optional[str] = None
    emergency_phone: Optional[str] = None
    conditions: Optional[List[str]] = []
    medications: Optional[List[MedicationItem]] = []
    allergies: Optional[List[str]] = []
    activity_level: Optional[str] = None
    smoking_status: Optional[str] = None
    alcohol_consumption: Optional[str] = None
    notes: Optional[str] = None

class ProfileCreate(ProfileBase):
    pass

class ProfileUpdate(ProfileBase):
    pass

class Profile(ProfileBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}

# Response Schemas
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

class UserResponse(BaseModel):
    user: User
    profile: Optional[Profile] = None

class MessageResponse(BaseModel):
    message: str
    success: bool = True

# Vital Schemas
class VitalBase(BaseModel):
    systolic_bp: Optional[int] = None
    diastolic_bp: Optional[int] = None
    heart_rate: Optional[int] = None
    glucose_mg_dl: Optional[float] = None
    hba1c: Optional[float] = None
    steps: Optional[int] = None
    calories_burned: Optional[float] = None
    active_minutes: Optional[int] = None
    sleep_hours: Optional[float] = None
    sleep_quality: Optional[str] = None
    weight_kg: Optional[float] = None
    body_fat_percentage: Optional[float] = None
    data_source: Optional[str] = None

class VitalCreate(VitalBase):
    timestamp: Optional[datetime] = None

class Vital(VitalBase):
    id: int
    user_id: int
    timestamp: datetime
    created_at: datetime

    model_config = {"from_attributes": True}

# Food Log Schemas
class FoodLogBase(BaseModel):
    food_item: str
    meal_type: Optional[str] = None
    calories: Optional[float] = None
    carbs_g: Optional[float] = None
    protein_g: Optional[float] = None
    fat_g: Optional[float] = None
    fiber_g: Optional[float] = None
    sodium_mg: Optional[float] = None
    sugar_g: Optional[float] = None
    portion_size: Optional[str] = None

class FoodLogCreate(FoodLogBase):
    timestamp: Optional[datetime] = None

class FoodLog(FoodLogBase):
    id: int
    user_id: int
    timestamp: datetime
    created_at: datetime

    model_config = {"from_attributes": True}

# Symptom Schemas
class SymptomBase(BaseModel):
    symptom: str
    severity: Optional[str] = None
    duration: Optional[str] = None
    triggers: Optional[List[str]] = None
    notes: Optional[str] = None

class SymptomCreate(SymptomBase):
    timestamp: Optional[datetime] = None

class Symptom(SymptomBase):
    id: int
    user_id: int
    timestamp: datetime
    created_at: datetime

    model_config = {"from_attributes": True}

# Risk Assessment Schemas
class RiskAssessmentBase(BaseModel):
    assessment_type: str
    risk_level: RiskLevel
    risk_score: float = Field(..., ge=0.0, le=1.0)
    confidence_score: Optional[float] = None
    model_version: Optional[str] = None
    features_used: Optional[Dict[str, Any]] = None
    shap_values: Optional[Dict[str, Any]] = None
    top_risk_factors: Optional[List[Dict[str, Any]]] = None
    predicted_probability: Optional[float] = None
    risk_reduction_potential: Optional[float] = None

class RiskAssessmentCreate(RiskAssessmentBase):
    pass

class RiskAssessment(RiskAssessmentBase):
    id: int
    user_id: int
    created_at: datetime

    model_config = {"from_attributes": True}

# Recommendation Schemas
class RecommendationBase(BaseModel):
    category: str
    title: str
    description: str
    ai_explanation: Optional[str] = None
    personalized_advice: Optional[str] = None
    priority: Optional[str] = None
    difficulty: Optional[str] = None
    estimated_impact: Optional[float] = None
    micro_goals: Optional[List[Dict[str, Any]]] = None
    safety_checked: bool = False
    guideline_aligned: bool = False
    contraindications: Optional[List[str]] = None

class RecommendationCreate(RecommendationBase):
    risk_assessment_id: Optional[int] = None

class RecommendationUpdate(BaseModel):
    completed: Optional[bool] = None
    is_active: Optional[bool] = None

class Recommendation(RecommendationBase):
    id: int
    user_id: int
    risk_assessment_id: Optional[int]
    is_active: bool
    completed: bool
    completed_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}

# Gamification Schemas
class GamificationBase(BaseModel):
    total_points: int = 0
    level: int = 1
    experience_points: int = 0
    current_streak: int = 0
    longest_streak: int = 0
    last_activity_date: Optional[date] = None
    badges_earned: Optional[List[str]] = []
    achievements: Optional[List[Dict[str, Any]]] = []
    active_challenges: Optional[List[Dict[str, Any]]] = []
    completed_challenges: Optional[List[Dict[str, Any]]] = []
    goals_completed: int = 0
    recommendations_followed: int = 0
    data_entries: int = 0

class GamificationUpdate(BaseModel):
    points_to_add: Optional[int] = None
    new_badge: Optional[str] = None
    completed_goal: Optional[bool] = None
    data_entry: Optional[bool] = None

class Gamification(GamificationBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}

# Prediction Request Schemas
class PredictionRequest(BaseModel):
    features: Dict[str, Any]
    model_type: Optional[str] = "diabetes"  # diabetes, hypertension

class PredictionResponse(BaseModel):
    risk_level: RiskLevel
    risk_score: float
    confidence_score: float
    top_risk_factors: List[Dict[str, Any]]
    recommendations: List[str]
    explanation: str
