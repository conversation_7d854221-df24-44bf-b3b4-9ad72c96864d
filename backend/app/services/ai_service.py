import pickle
import numpy as np
import pandas as pd
import shap
from typing import Dict, Any, List, Tuple
from sklearn.ensemble import VotingClassifier
import logging
import os

logger = logging.getLogger(__name__)

class AIModelService:
    def __init__(self):
        self.models = {}
        self.explainers = {}
        self.feature_columns = {}
        self.load_models()
    
    def load_models(self):
        """Load pre-trained models and create SHAP explainers"""
        try:
            # Load the ensemble model
            model_path = os.path.join(os.path.dirname(__file__), "../../../final_lr_xgb_ensemble.pkl")
            if os.path.exists(model_path):
                with open(model_path, 'rb') as f:
                    self.models['diabetes'] = pickle.load(f)
                    self.models['hypertension'] = self.models['diabetes']  # Using same model for now
                
                # Define expected feature columns based on common health metrics
                self.feature_columns['diabetes'] = [
                    'age', 'gender', 'bmi', 'glucose', 'systolic_bp', 'diastolic_bp',
                    'heart_rate', 'hba1c', 'activity_level', 'smoking_status',
                    'family_history', 'sleep_hours', 'stress_level'
                ]
                
                self.feature_columns['hypertension'] = [
                    'age', 'gender', 'bmi', 'systolic_bp', 'diastolic_bp',
                    'heart_rate', 'sodium_intake', 'activity_level', 'smoking_status',
                    'alcohol_consumption', 'family_history', 'stress_level'
                ]
                
                # Create SHAP explainers
                self._create_explainers()
                
                logger.info("AI models loaded successfully")
            else:
                logger.warning(f"Model file not found at {model_path}")
                
        except Exception as e:
            logger.error(f"Error loading AI models: {e}")
    
    def _create_explainers(self):
        """Create SHAP explainers for model interpretability"""
        try:
            for model_type in ['diabetes', 'hypertension']:
                if model_type in self.models:
                    # Create sample data for explainer initialization
                    sample_data = self._create_sample_data(model_type)
                    
                    # Use TreeExplainer for ensemble models
                    if hasattr(self.models[model_type], 'estimators_'):
                        # For VotingClassifier, use the first estimator (XGBoost)
                        xgb_model = None
                        for name, estimator in self.models[model_type].estimators_:
                            if 'XGBoost' in name or 'xgb' in name.lower():
                                xgb_model = estimator
                                break
                        
                        if xgb_model:
                            self.explainers[model_type] = shap.TreeExplainer(xgb_model)
                        else:
                            # Fallback to KernelExplainer
                            self.explainers[model_type] = shap.KernelExplainer(
                                self.models[model_type].predict_proba, sample_data
                            )
                    else:
                        # For single models
                        self.explainers[model_type] = shap.KernelExplainer(
                            self.models[model_type].predict_proba, sample_data
                        )
                        
        except Exception as e:
            logger.error(f"Error creating SHAP explainers: {e}")
    
    def _create_sample_data(self, model_type: str) -> np.ndarray:
        """Create sample data for SHAP explainer initialization"""
        features = self.feature_columns[model_type]
        # Create realistic sample data
        sample_data = []
        for _ in range(100):
            row = []
            for feature in features:
                if feature == 'age':
                    row.append(np.random.randint(18, 80))
                elif feature == 'gender':
                    row.append(np.random.randint(0, 2))  # 0: female, 1: male
                elif feature == 'bmi':
                    row.append(np.random.uniform(18.5, 35.0))
                elif 'bp' in feature:
                    if 'systolic' in feature:
                        row.append(np.random.randint(90, 180))
                    else:
                        row.append(np.random.randint(60, 120))
                elif feature == 'glucose':
                    row.append(np.random.uniform(70, 200))
                elif feature == 'heart_rate':
                    row.append(np.random.randint(60, 100))
                elif feature == 'hba1c':
                    row.append(np.random.uniform(4.0, 10.0))
                elif 'hours' in feature:
                    row.append(np.random.uniform(4, 10))
                else:
                    row.append(np.random.uniform(0, 1))
            sample_data.append(row)
        
        return np.array(sample_data)
    
    def prepare_features(self, user_data: Dict[str, Any], model_type: str) -> np.ndarray:
        """Prepare features for model prediction"""
        features = self.feature_columns.get(model_type, [])
        feature_values = []
        
        for feature in features:
            if feature == 'age':
                feature_values.append(user_data.get('age', 35))
            elif feature == 'gender':
                gender = user_data.get('gender', 'unknown').lower()
                feature_values.append(1 if gender == 'male' else 0)
            elif feature == 'bmi':
                height = user_data.get('height_cm', 170) / 100  # Convert to meters
                weight = user_data.get('weight_kg', 70)
                bmi = weight / (height ** 2) if height > 0 else 25
                feature_values.append(bmi)
            elif feature == 'glucose':
                feature_values.append(user_data.get('glucose_mg_dl', 100))
            elif feature == 'systolic_bp':
                feature_values.append(user_data.get('systolic_bp', 120))
            elif feature == 'diastolic_bp':
                feature_values.append(user_data.get('diastolic_bp', 80))
            elif feature == 'heart_rate':
                feature_values.append(user_data.get('heart_rate', 70))
            elif feature == 'hba1c':
                feature_values.append(user_data.get('hba1c', 5.5))
            elif feature == 'activity_level':
                activity = user_data.get('activity_level', 'moderate')
                activity_map = {'sedentary': 0, 'light': 1, 'moderate': 2, 'active': 3}
                feature_values.append(activity_map.get(activity, 2))
            elif feature == 'smoking_status':
                smoking = user_data.get('smoking_status', 'never')
                smoking_map = {'never': 0, 'former': 1, 'current': 2}
                feature_values.append(smoking_map.get(smoking, 0))
            elif feature == 'alcohol_consumption':
                alcohol = user_data.get('alcohol_consumption', 'none')
                alcohol_map = {'none': 0, 'light': 1, 'moderate': 2, 'heavy': 3}
                feature_values.append(alcohol_map.get(alcohol, 0))
            elif feature == 'sleep_hours':
                feature_values.append(user_data.get('sleep_hours', 7.5))
            elif feature == 'family_history':
                # Simplified: check if any conditions indicate family history
                conditions = user_data.get('conditions', [])
                has_family_history = any('family' in str(condition).lower() for condition in conditions)
                feature_values.append(1 if has_family_history else 0)
            elif feature == 'stress_level':
                # Default moderate stress level
                feature_values.append(user_data.get('stress_level', 2))
            elif feature == 'sodium_intake':
                # Estimate from food logs or default
                feature_values.append(user_data.get('sodium_mg', 2300))
            else:
                # Default value for unknown features
                feature_values.append(0)
        
        return np.array(feature_values).reshape(1, -1)
    
    def predict_risk(self, user_data: Dict[str, Any], model_type: str = 'diabetes') -> Dict[str, Any]:
        """Predict health risk and provide explanations"""
        try:
            if model_type not in self.models:
                raise ValueError(f"Model type '{model_type}' not available")
            
            # Prepare features
            features = self.prepare_features(user_data, model_type)
            
            # Make prediction
            model = self.models[model_type]
            risk_probability = model.predict_proba(features)[0][1]  # Probability of positive class
            risk_prediction = model.predict(features)[0]
            
            # Determine risk level
            if risk_probability < 0.3:
                risk_level = "low"
            elif risk_probability < 0.7:
                risk_level = "medium"
            else:
                risk_level = "high"
            
            # Get SHAP explanations
            shap_values = None
            top_risk_factors = []
            
            if model_type in self.explainers:
                try:
                    explainer = self.explainers[model_type]
                    shap_values = explainer.shap_values(features)
                    
                    # Handle different SHAP output formats
                    if isinstance(shap_values, list):
                        shap_values = shap_values[1]  # Use positive class
                    
                    # Get top risk factors
                    feature_names = self.feature_columns[model_type]
                    feature_importance = list(zip(feature_names, shap_values[0]))
                    feature_importance.sort(key=lambda x: abs(x[1]), reverse=True)
                    
                    top_risk_factors = [
                        {
                            "feature": name,
                            "importance": float(importance),
                            "impact": "increases risk" if importance > 0 else "decreases risk"
                        }
                        for name, importance in feature_importance[:3]
                    ]
                    
                except Exception as e:
                    logger.warning(f"Error generating SHAP explanations: {e}")
            
            return {
                "risk_level": risk_level,
                "risk_score": float(risk_probability),
                "confidence_score": float(max(risk_probability, 1 - risk_probability)),
                "predicted_probability": float(risk_probability),
                "top_risk_factors": top_risk_factors,
                "model_version": "ensemble_v1.0",
                "features_used": dict(zip(self.feature_columns[model_type], features[0].tolist()))
            }
            
        except Exception as e:
            logger.error(f"Error in risk prediction: {e}")
            raise
    
    def get_risk_explanation(self, risk_data: Dict[str, Any]) -> str:
        """Generate human-readable explanation of risk assessment"""
        risk_level = risk_data.get('risk_level', 'unknown')
        risk_score = risk_data.get('risk_score', 0)
        top_factors = risk_data.get('top_risk_factors', [])
        
        explanation = f"Your {risk_level} risk assessment indicates a {risk_score:.1%} probability. "
        
        if top_factors:
            explanation += "The main factors contributing to your risk are: "
            factor_descriptions = []
            for factor in top_factors[:3]:
                factor_descriptions.append(f"{factor['feature']} ({factor['impact']})")
            explanation += ", ".join(factor_descriptions) + ". "
        
        return explanation

# Global instance
ai_service = AIModelService()
