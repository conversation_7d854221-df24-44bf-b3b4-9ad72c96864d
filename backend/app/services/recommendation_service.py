import json
import logging
from typing import Dict, Any, List
import openai
import os
from datetime import datetime

logger = logging.getLogger(__name__)

class RecommendationService:
    def __init__(self):
        self.openai_client = None
        self.setup_openai()
        self.load_recommendation_rules()
    
    def setup_openai(self):
        """Setup OpenAI client for GenAI recommendations"""
        api_key = os.getenv('OPENAI_API_KEY')
        if api_key:
            self.openai_client = openai.OpenAI(api_key=api_key)
        else:
            logger.warning("OpenAI API key not found. GenAI features will be limited.")
    
    def load_recommendation_rules(self):
        """Load rule-based recommendation engine"""
        self.rules = {
            "diabetes": {
                "high_glucose": {
                    "category": "diet",
                    "title": "Blood Sugar Management",
                    "description": "Your glucose levels are elevated. Focus on low-glycemic foods and portion control.",
                    "micro_goals": [
                        {"goal": "Reduce refined carbs", "target": "< 50g per meal", "benefit": "15% risk reduction"},
                        {"goal": "Increase fiber intake", "target": "25-30g daily", "benefit": "10% risk reduction"},
                        {"goal": "Monitor blood sugar", "target": "3x daily", "benefit": "Better control"}
                    ],
                    "priority": "high",
                    "difficulty": "moderate"
                },
                "high_bmi": {
                    "category": "exercise",
                    "title": "Weight Management",
                    "description": "Your BMI indicates increased diabetes risk. Regular exercise can help.",
                    "micro_goals": [
                        {"goal": "Daily walking", "target": "30 minutes", "benefit": "12% risk reduction"},
                        {"goal": "Strength training", "target": "2x per week", "benefit": "8% risk reduction"},
                        {"goal": "Weight loss", "target": "5-10% body weight", "benefit": "20% risk reduction"}
                    ],
                    "priority": "high",
                    "difficulty": "moderate"
                },
                "sedentary": {
                    "category": "exercise",
                    "title": "Increase Physical Activity",
                    "description": "Regular physical activity is crucial for diabetes prevention.",
                    "micro_goals": [
                        {"goal": "Take stairs", "target": "instead of elevator", "benefit": "5% risk reduction"},
                        {"goal": "Park farther", "target": "add 500 steps daily", "benefit": "3% risk reduction"},
                        {"goal": "Stand hourly", "target": "during work", "benefit": "Improved metabolism"}
                    ],
                    "priority": "medium",
                    "difficulty": "easy"
                }
            },
            "hypertension": {
                "high_bp": {
                    "category": "diet",
                    "title": "Blood Pressure Control",
                    "description": "Your blood pressure is elevated. Dietary changes can help significantly.",
                    "micro_goals": [
                        {"goal": "Reduce sodium", "target": "< 2300mg daily", "benefit": "15% risk reduction"},
                        {"goal": "DASH diet", "target": "Follow guidelines", "benefit": "20% risk reduction"},
                        {"goal": "Limit alcohol", "target": "< 2 drinks daily", "benefit": "10% risk reduction"}
                    ],
                    "priority": "high",
                    "difficulty": "moderate"
                },
                "high_stress": {
                    "category": "stress",
                    "title": "Stress Management",
                    "description": "Chronic stress contributes to hypertension. Learn stress reduction techniques.",
                    "micro_goals": [
                        {"goal": "Deep breathing", "target": "5 minutes daily", "benefit": "8% risk reduction"},
                        {"goal": "Meditation", "target": "10 minutes daily", "benefit": "12% risk reduction"},
                        {"goal": "Regular sleep", "target": "7-8 hours nightly", "benefit": "10% risk reduction"}
                    ],
                    "priority": "medium",
                    "difficulty": "easy"
                }
            }
        }
    
    def generate_recommendations(self, risk_assessment: Dict[str, Any], user_profile: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate personalized recommendations based on risk assessment"""
        recommendations = []
        
        assessment_type = risk_assessment.get('assessment_type', 'diabetes')
        risk_level = risk_assessment.get('risk_level', 'low')
        top_factors = risk_assessment.get('top_risk_factors', [])
        
        # Rule-based recommendations
        rule_recommendations = self._get_rule_based_recommendations(
            assessment_type, risk_level, top_factors, user_profile
        )
        recommendations.extend(rule_recommendations)
        
        # GenAI enhanced recommendations
        if self.openai_client:
            ai_recommendations = self._get_ai_recommendations(
                risk_assessment, user_profile, rule_recommendations
            )
            recommendations.extend(ai_recommendations)
        
        # Apply safety filters
        recommendations = self._apply_safety_filters(recommendations, user_profile)
        
        return recommendations
    
    def _get_rule_based_recommendations(self, assessment_type: str, risk_level: str, 
                                      top_factors: List[Dict], user_profile: Dict) -> List[Dict[str, Any]]:
        """Generate rule-based recommendations"""
        recommendations = []
        
        if assessment_type not in self.rules:
            return recommendations
        
        rules = self.rules[assessment_type]
        
        # Analyze top risk factors and map to rules
        for factor in top_factors:
            feature = factor.get('feature', '')
            importance = factor.get('importance', 0)
            
            # Map features to recommendation rules
            if 'glucose' in feature.lower() and 'high_glucose' in rules:
                rec = rules['high_glucose'].copy()
                rec['estimated_impact'] = min(abs(importance) * 20, 25)  # Scale impact
                recommendations.append(rec)
            
            elif 'bmi' in feature.lower() and 'high_bmi' in rules:
                rec = rules['high_bmi'].copy()
                rec['estimated_impact'] = min(abs(importance) * 15, 20)
                recommendations.append(rec)
            
            elif 'activity' in feature.lower() and 'sedentary' in rules:
                rec = rules['sedentary'].copy()
                rec['estimated_impact'] = min(abs(importance) * 10, 15)
                recommendations.append(rec)
            
            elif 'bp' in feature.lower() and 'high_bp' in rules:
                rec = rules['high_bp'].copy()
                rec['estimated_impact'] = min(abs(importance) * 18, 25)
                recommendations.append(rec)
            
            elif 'stress' in feature.lower() and 'high_stress' in rules:
                rec = rules['high_stress'].copy()
                rec['estimated_impact'] = min(abs(importance) * 12, 18)
                recommendations.append(rec)
        
        # Add general recommendations based on risk level
        if risk_level == 'high' and not recommendations:
            # Add high-priority general recommendations
            if assessment_type == 'diabetes':
                recommendations.append(rules.get('high_glucose', {}))
            elif assessment_type == 'hypertension':
                recommendations.append(rules.get('high_bp', {}))
        
        return recommendations
    
    def _get_ai_recommendations(self, risk_assessment: Dict[str, Any], 
                               user_profile: Dict[str, Any], 
                               rule_recommendations: List[Dict]) -> List[Dict[str, Any]]:
        """Generate AI-enhanced personalized recommendations"""
        try:
            # Prepare context for AI
            context = {
                "risk_assessment": risk_assessment,
                "user_profile": {
                    "age": user_profile.get('age'),
                    "gender": user_profile.get('gender'),
                    "conditions": user_profile.get('conditions', []),
                    "medications": user_profile.get('medications', []),
                    "activity_level": user_profile.get('activity_level'),
                    "smoking_status": user_profile.get('smoking_status')
                },
                "existing_recommendations": [r.get('title', '') for r in rule_recommendations]
            }
            
            prompt = self._create_ai_prompt(context)
            
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a certified health coach specializing in diabetes and hypertension prevention. Provide evidence-based, safe, and personalized health recommendations."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.7
            )
            
            ai_content = response.choices[0].message.content
            
            # Parse AI response and create recommendation objects
            ai_recommendations = self._parse_ai_response(ai_content)
            
            return ai_recommendations
            
        except Exception as e:
            logger.error(f"Error generating AI recommendations: {e}")
            return []
    
    def _create_ai_prompt(self, context: Dict[str, Any]) -> str:
        """Create prompt for AI recommendation generation"""
        risk_level = context['risk_assessment'].get('risk_level', 'unknown')
        assessment_type = context['risk_assessment'].get('assessment_type', 'diabetes')
        top_factors = context['risk_assessment'].get('top_risk_factors', [])
        user_profile = context['user_profile']
        
        prompt = f"""
        Generate 2-3 personalized health recommendations for a {user_profile.get('age', 'adult')} year old {user_profile.get('gender', 'person')} with {risk_level} risk of {assessment_type}.

        Risk Factors:
        {json.dumps(top_factors, indent=2)}

        User Profile:
        - Medical conditions: {user_profile.get('conditions', [])}
        - Current medications: {user_profile.get('medications', [])}
        - Activity level: {user_profile.get('activity_level', 'unknown')}
        - Smoking status: {user_profile.get('smoking_status', 'unknown')}

        Existing recommendations: {context.get('existing_recommendations', [])}

        Please provide recommendations in this JSON format:
        {{
            "recommendations": [
                {{
                    "category": "diet|exercise|sleep|stress|medication",
                    "title": "Brief title",
                    "description": "Detailed description",
                    "ai_explanation": "Why this is important for this specific user",
                    "personalized_advice": "Specific actionable advice tailored to user",
                    "priority": "low|medium|high",
                    "difficulty": "easy|moderate|hard",
                    "estimated_impact": "percentage as number",
                    "micro_goals": [
                        {{"goal": "specific action", "target": "measurable target", "benefit": "expected benefit"}}
                    ],
                    "safety_checked": true,
                    "guideline_aligned": true
                }}
            ]
        }}

        Ensure all recommendations are:
        1. Evidence-based and aligned with medical guidelines
        2. Safe for the user's profile
        3. Actionable and specific
        4. Different from existing recommendations
        """
        
        return prompt
    
    def _parse_ai_response(self, ai_content: str) -> List[Dict[str, Any]]:
        """Parse AI response into recommendation objects"""
        try:
            # Try to extract JSON from the response
            start_idx = ai_content.find('{')
            end_idx = ai_content.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = ai_content[start_idx:end_idx]
                parsed = json.loads(json_str)
                return parsed.get('recommendations', [])
            
        except json.JSONDecodeError:
            logger.warning("Failed to parse AI response as JSON")
        
        return []
    
    def _apply_safety_filters(self, recommendations: List[Dict[str, Any]], 
                            user_profile: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Apply safety filters to recommendations"""
        filtered_recommendations = []
        
        user_conditions = user_profile.get('conditions', [])
        user_medications = user_profile.get('medications', [])
        user_age = user_profile.get('age', 30)
        
        for rec in recommendations:
            # Age-based filtering
            if user_age > 65 and rec.get('difficulty') == 'hard':
                rec['difficulty'] = 'moderate'
                rec['description'] += " (Modified for senior safety)"
            
            # Condition-based filtering
            contraindications = []
            
            # Check for heart conditions
            if any('heart' in str(condition).lower() for condition in user_conditions):
                if rec.get('category') == 'exercise' and rec.get('difficulty') == 'hard':
                    contraindications.append("Consult cardiologist before high-intensity exercise")
            
            # Check for kidney conditions
            if any('kidney' in str(condition).lower() for condition in user_conditions):
                if 'protein' in rec.get('description', '').lower():
                    contraindications.append("Monitor protein intake with kidney condition")
            
            # Medication interactions
            if any('blood thinner' in str(med).lower() for med in user_medications):
                if 'supplement' in rec.get('description', '').lower():
                    contraindications.append("Check supplement interactions with blood thinners")
            
            rec['contraindications'] = contraindications
            rec['safety_checked'] = True
            rec['guideline_aligned'] = True
            
            filtered_recommendations.append(rec)
        
        return filtered_recommendations

# Global instance
recommendation_service = RecommendationService()
