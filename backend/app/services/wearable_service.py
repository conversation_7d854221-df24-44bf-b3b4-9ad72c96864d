import httpx
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import os
from cryptography.fernet import Fernet

logger = logging.getLogger(__name__)

class WearableIntegrationService:
    def __init__(self):
        self.encryption_key = self._get_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key)
        
        # API configurations
        self.api_configs = {
            'fitbit': {
                'base_url': 'https://api.fitbit.com/1',
                'auth_url': 'https://www.fitbit.com/oauth2/authorize',
                'token_url': 'https://api.fitbit.com/oauth2/token',
                'client_id': os.getenv('FITBIT_CLIENT_ID'),
                'client_secret': os.getenv('FITBIT_CLIENT_SECRET'),
                'scopes': ['activity', 'heartrate', 'sleep', 'weight']
            },
            'dexcom': {
                'base_url': 'https://sandbox-api.dexcom.com/v2',
                'auth_url': 'https://sandbox-api.dexcom.com/v2/oauth2/login',
                'token_url': 'https://sandbox-api.dexcom.com/v2/oauth2/token',
                'client_id': os.getenv('DEXCOM_CLIENT_ID'),
                'client_secret': os.getenv('DEXCOM_CLIENT_SECRET'),
                'scopes': ['offline_access']
            },
            'google_fit': {
                'base_url': 'https://www.googleapis.com/fitness/v1',
                'auth_url': 'https://accounts.google.com/o/oauth2/auth',
                'token_url': 'https://oauth2.googleapis.com/token',
                'client_id': os.getenv('GOOGLE_FIT_CLIENT_ID'),
                'client_secret': os.getenv('GOOGLE_FIT_CLIENT_SECRET'),
                'scopes': [
                    'https://www.googleapis.com/auth/fitness.activity.read',
                    'https://www.googleapis.com/auth/fitness.heart_rate.read',
                    'https://www.googleapis.com/auth/fitness.sleep.read'
                ]
            }
        }
    
    def _get_encryption_key(self) -> bytes:
        """Get or generate encryption key for tokens"""
        key = os.getenv('ENCRYPTION_KEY')
        if key:
            return key.encode()
        else:
            # Generate a new key (in production, store this securely)
            return Fernet.generate_key()
    
    def encrypt_token(self, token: str) -> str:
        """Encrypt access token for storage"""
        return self.cipher_suite.encrypt(token.encode()).decode()
    
    def decrypt_token(self, encrypted_token: str) -> str:
        """Decrypt access token for use"""
        return self.cipher_suite.decrypt(encrypted_token.encode()).decode()
    
    def get_authorization_url(self, provider: str, user_id: int) -> Dict[str, Any]:
        """Generate OAuth authorization URL for wearable provider"""
        if provider not in self.api_configs:
            raise ValueError(f"Unsupported provider: {provider}")
        
        config = self.api_configs[provider]
        
        if provider == 'fitbit':
            params = {
                'response_type': 'code',
                'client_id': config['client_id'],
                'redirect_uri': f"{os.getenv('BACKEND_URL', 'http://localhost:8000')}/api/wearables/callback/{provider}",
                'scope': ' '.join(config['scopes']),
                'state': str(user_id)  # Pass user ID in state
            }
        elif provider == 'dexcom':
            params = {
                'response_type': 'code',
                'client_id': config['client_id'],
                'redirect_uri': f"{os.getenv('BACKEND_URL', 'http://localhost:8000')}/api/wearables/callback/{provider}",
                'scope': ' '.join(config['scopes']),
                'state': str(user_id)
            }
        elif provider == 'google_fit':
            params = {
                'response_type': 'code',
                'client_id': config['client_id'],
                'redirect_uri': f"{os.getenv('BACKEND_URL', 'http://localhost:8000')}/api/wearables/callback/{provider}",
                'scope': ' '.join(config['scopes']),
                'access_type': 'offline',
                'state': str(user_id)
            }
        
        # Build URL
        param_string = '&'.join([f"{k}={v}" for k, v in params.items()])
        auth_url = f"{config['auth_url']}?{param_string}"
        
        return {
            'authorization_url': auth_url,
            'provider': provider,
            'state': str(user_id)
        }
    
    async def exchange_code_for_token(self, provider: str, code: str, user_id: int) -> Dict[str, Any]:
        """Exchange authorization code for access token"""
        if provider not in self.api_configs:
            raise ValueError(f"Unsupported provider: {provider}")
        
        config = self.api_configs[provider]
        
        token_data = {
            'grant_type': 'authorization_code',
            'client_id': config['client_id'],
            'client_secret': config['client_secret'],
            'code': code,
            'redirect_uri': f"{os.getenv('BACKEND_URL', 'http://localhost:8000')}/api/wearables/callback/{provider}"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(config['token_url'], data=token_data)
            
            if response.status_code == 200:
                token_info = response.json()
                
                # Encrypt tokens before returning
                encrypted_access_token = self.encrypt_token(token_info['access_token'])
                encrypted_refresh_token = None
                
                if 'refresh_token' in token_info:
                    encrypted_refresh_token = self.encrypt_token(token_info['refresh_token'])
                
                return {
                    'access_token': encrypted_access_token,
                    'refresh_token': encrypted_refresh_token,
                    'expires_in': token_info.get('expires_in'),
                    'token_type': token_info.get('token_type', 'Bearer'),
                    'scope': token_info.get('scope')
                }
            else:
                raise Exception(f"Token exchange failed: {response.text}")
    
    async def sync_fitbit_data(self, integration: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Sync data from Fitbit API"""
        try:
            access_token = self.decrypt_token(integration['access_token'])
            headers = {'Authorization': f"Bearer {access_token}"}
            
            vitals_data = []
            today = datetime.now().strftime('%Y-%m-%d')
            
            async with httpx.AsyncClient() as client:
                # Get heart rate data
                hr_response = await client.get(
                    f"{self.api_configs['fitbit']['base_url']}/user/-/activities/heart/date/{today}/1d.json",
                    headers=headers
                )
                
                if hr_response.status_code == 200:
                    hr_data = hr_response.json()
                    if 'activities-heart' in hr_data and hr_data['activities-heart']:
                        heart_data = hr_data['activities-heart'][0]
                        if 'value' in heart_data and 'restingHeartRate' in heart_data['value']:
                            vitals_data.append({
                                'heart_rate': heart_data['value']['restingHeartRate'],
                                'timestamp': datetime.now(),
                                'data_source': 'fitbit'
                            })
                
                # Get steps data
                steps_response = await client.get(
                    f"{self.api_configs['fitbit']['base_url']}/user/-/activities/steps/date/{today}/1d.json",
                    headers=headers
                )
                
                if steps_response.status_code == 200:
                    steps_data = steps_response.json()
                    if 'activities-steps' in steps_data and steps_data['activities-steps']:
                        steps_value = steps_data['activities-steps'][0]['value']
                        if vitals_data:
                            vitals_data[0]['steps'] = int(steps_value)
                        else:
                            vitals_data.append({
                                'steps': int(steps_value),
                                'timestamp': datetime.now(),
                                'data_source': 'fitbit'
                            })
                
                # Get sleep data
                sleep_response = await client.get(
                    f"{self.api_configs['fitbit']['base_url']}/user/-/sleep/date/{today}.json",
                    headers=headers
                )
                
                if sleep_response.status_code == 200:
                    sleep_data = sleep_response.json()
                    if 'sleep' in sleep_data and sleep_data['sleep']:
                        sleep_record = sleep_data['sleep'][0]
                        sleep_hours = sleep_record.get('duration', 0) / (1000 * 60 * 60)  # Convert ms to hours
                        
                        if vitals_data:
                            vitals_data[0]['sleep_hours'] = sleep_hours
                        else:
                            vitals_data.append({
                                'sleep_hours': sleep_hours,
                                'timestamp': datetime.now(),
                                'data_source': 'fitbit'
                            })
            
            return vitals_data
            
        except Exception as e:
            logger.error(f"Error syncing Fitbit data: {e}")
            return []
    
    async def sync_dexcom_data(self, integration: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Sync glucose data from Dexcom API"""
        try:
            access_token = self.decrypt_token(integration['access_token'])
            headers = {'Authorization': f"Bearer {access_token}"}
            
            # Get glucose readings from last 24 hours
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=24)
            
            params = {
                'startDate': start_time.isoformat(),
                'endDate': end_time.isoformat()
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.api_configs['dexcom']['base_url']}/users/self/egvs",
                    headers=headers,
                    params=params
                )
                
                if response.status_code == 200:
                    glucose_data = response.json()
                    vitals_data = []
                    
                    for reading in glucose_data.get('egvs', []):
                        vitals_data.append({
                            'glucose_mg_dl': reading['value'],
                            'timestamp': datetime.fromisoformat(reading['systemTime'].replace('Z', '+00:00')),
                            'data_source': 'dexcom'
                        })
                    
                    return vitals_data
                else:
                    logger.warning(f"Dexcom API error: {response.status_code} - {response.text}")
                    return []
            
        except Exception as e:
            logger.error(f"Error syncing Dexcom data: {e}")
            return []
    
    async def sync_google_fit_data(self, integration: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Sync data from Google Fit API"""
        try:
            access_token = self.decrypt_token(integration['access_token'])
            headers = {'Authorization': f"Bearer {access_token}"}
            
            # Get data from last 24 hours
            end_time = int(datetime.now().timestamp() * 1000000000)  # nanoseconds
            start_time = int((datetime.now() - timedelta(hours=24)).timestamp() * 1000000000)
            
            vitals_data = []
            
            async with httpx.AsyncClient() as client:
                # Get step count
                steps_body = {
                    "aggregateBy": [{"dataTypeName": "com.google.step_count.delta"}],
                    "bucketByTime": {"durationMillis": 86400000},  # 1 day
                    "startTimeMillis": start_time // 1000000,  # milliseconds
                    "endTimeMillis": end_time // 1000000
                }
                
                steps_response = await client.post(
                    f"{self.api_configs['google_fit']['base_url']}/users/me/dataset:aggregate",
                    headers=headers,
                    json=steps_body
                )
                
                if steps_response.status_code == 200:
                    steps_data = steps_response.json()
                    total_steps = 0
                    
                    for bucket in steps_data.get('bucket', []):
                        for dataset in bucket.get('dataset', []):
                            for point in dataset.get('point', []):
                                for value in point.get('value', []):
                                    total_steps += value.get('intVal', 0)
                    
                    if total_steps > 0:
                        vitals_data.append({
                            'steps': total_steps,
                            'timestamp': datetime.now(),
                            'data_source': 'google_fit'
                        })
                
                # Get heart rate data
                hr_body = {
                    "aggregateBy": [{"dataTypeName": "com.google.heart_rate.bpm"}],
                    "bucketByTime": {"durationMillis": 86400000},
                    "startTimeMillis": start_time // 1000000,
                    "endTimeMillis": end_time // 1000000
                }
                
                hr_response = await client.post(
                    f"{self.api_configs['google_fit']['base_url']}/users/me/dataset:aggregate",
                    headers=headers,
                    json=hr_body
                )
                
                if hr_response.status_code == 200:
                    hr_data = hr_response.json()
                    heart_rates = []
                    
                    for bucket in hr_data.get('bucket', []):
                        for dataset in bucket.get('dataset', []):
                            for point in dataset.get('point', []):
                                for value in point.get('value', []):
                                    heart_rates.append(value.get('fpVal', 0))
                    
                    if heart_rates:
                        avg_hr = sum(heart_rates) / len(heart_rates)
                        if vitals_data:
                            vitals_data[0]['heart_rate'] = int(avg_hr)
                        else:
                            vitals_data.append({
                                'heart_rate': int(avg_hr),
                                'timestamp': datetime.now(),
                                'data_source': 'google_fit'
                            })
            
            return vitals_data
            
        except Exception as e:
            logger.error(f"Error syncing Google Fit data: {e}")
            return []
    
    async def sync_provider_data(self, provider: str, integration: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Sync data from specified provider"""
        if provider == 'fitbit':
            return await self.sync_fitbit_data(integration)
        elif provider == 'dexcom':
            return await self.sync_dexcom_data(integration)
        elif provider == 'google_fit':
            return await self.sync_google_fit_data(integration)
        else:
            raise ValueError(f"Unsupported provider: {provider}")

# Global instance
wearable_service = WearableIntegrationService()
