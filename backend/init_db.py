#!/usr/bin/env python3
"""
Database initialization script for HealthCoach AI
Creates tables and populates with sample data
"""

import os
import sys
from datetime import datetime, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.database import Base, get_database_url
from app.models import (
    User, Profile, Vital, RiskAssessment, Recommendation, 
    Gamification, Consent, AuditLog, WearableIntegration, UserType, RiskLevel
)
from app.auth import get_password_hash

def create_database():
    """Create database tables"""
    print("Creating database tables...")
    
    # Get database URL
    database_url = get_database_url()
    engine = create_engine(database_url)
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    print("✅ Database tables created successfully")
    
    return engine

def create_sample_data(engine):
    """Create sample data for testing"""
    print("Creating sample data...")
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Create sample patient user
        patient_user = User(
            username='patient_demo',
            email='<EMAIL>',
            password_hash=get_password_hash('demo123'),
            user_type=UserType.PATIENT,
            is_active=True,
            created_at=datetime.utcnow()
        )
        db.add(patient_user)
        db.commit()
        db.refresh(patient_user)
        
        # Create profile for patient
        patient_profile = Profile(
            user_id=patient_user.id,
            name='Demo Patient',
            age=35,
            gender='male',
            height_cm=175.0,
            weight_kg=80.0,
            activity_level='moderate',
            medical_history={'conditions': ['none'], 'allergies': [], 'medications': []},
            emergency_contact={'name': 'Jane Doe', 'phone': '+**********', 'relationship': 'spouse'}
        )
        db.add(patient_profile)
        
        # Create sample clinician user
        clinician_user = User(
            username='clinician_demo',
            email='<EMAIL>',
            password_hash=get_password_hash('demo123'),
            user_type=UserType.CLINICIAN,
            is_active=True,
            created_at=datetime.utcnow()
        )
        db.add(clinician_user)
        db.commit()
        db.refresh(clinician_user)
        
        # Create profile for clinician
        clinician_profile = Profile(
            user_id=clinician_user.id,
            name='Dr. Demo Clinician',
            specialization='Endocrinology',
            license_number='MD123456',
            years_experience=10
        )
        db.add(clinician_profile)
        
        # Create sample vital signs for patient
        vitals_data = [
            {
                'user_id': patient_user.id,
                'vital_type': 'blood_pressure',
                'value': 120.0,
                'unit': 'mmHg',
                'additional_data': {'systolic': 120, 'diastolic': 80},
                'recorded_at': datetime.utcnow() - timedelta(hours=2)
            },
            {
                'user_id': patient_user.id,
                'vital_type': 'heart_rate',
                'value': 72.0,
                'unit': 'bpm',
                'recorded_at': datetime.utcnow() - timedelta(hours=1)
            },
            {
                'user_id': patient_user.id,
                'vital_type': 'glucose',
                'value': 95.0,
                'unit': 'mg/dL',
                'recorded_at': datetime.utcnow() - timedelta(minutes=30)
            },
            {
                'user_id': patient_user.id,
                'vital_type': 'weight',
                'value': 80.0,
                'unit': 'kg',
                'recorded_at': datetime.utcnow() - timedelta(days=1)
            }
        ]
        
        for vital_data in vitals_data:
            vital = Vital(**vital_data)
            db.add(vital)
        
        # Create sample risk assessment
        risk_assessment = RiskAssessment(
            user_id=patient_user.id,
            diabetes_risk=0.15,
            hypertension_risk=0.25,
            overall_risk_level=RiskLevel.LOW,
            risk_factors=['family_history', 'age'],
            shap_explanations={
                'diabetes': {
                    'glucose_level': 0.05,
                    'bmi': 0.03,
                    'age': 0.04,
                    'family_history': 0.03
                },
                'hypertension': {
                    'blood_pressure': 0.08,
                    'sodium_intake': 0.06,
                    'stress_level': 0.05,
                    'exercise_frequency': -0.04
                }
            },
            confidence_score=0.87,
            created_at=datetime.utcnow()
        )
        db.add(risk_assessment)
        
        # Create sample recommendations
        recommendations_data = [
            {
                'user_id': patient_user.id,
                'title': 'Take a 30-minute walk daily',
                'description': 'Regular walking helps improve cardiovascular health and blood sugar control.',
                'category': 'exercise',
                'priority': 'high',
                'estimated_impact': '12% risk reduction',
                'is_completed': False,
                'created_at': datetime.utcnow()
            },
            {
                'user_id': patient_user.id,
                'title': 'Reduce sodium intake to <2300mg daily',
                'description': 'Lower sodium intake can help reduce blood pressure.',
                'category': 'diet',
                'priority': 'medium',
                'estimated_impact': '8% risk reduction',
                'is_completed': True,
                'completed_at': datetime.utcnow() - timedelta(days=2),
                'created_at': datetime.utcnow() - timedelta(days=5)
            },
            {
                'user_id': patient_user.id,
                'title': 'Practice deep breathing for 10 minutes',
                'description': 'Stress reduction techniques can help lower blood pressure.',
                'category': 'stress_management',
                'priority': 'low',
                'estimated_impact': '5% risk reduction',
                'is_completed': False,
                'created_at': datetime.utcnow()
            }
        ]
        
        for rec_data in recommendations_data:
            recommendation = Recommendation(**rec_data)
            db.add(recommendation)
        
        # Create gamification data
        gamification = Gamification(
            user_id=patient_user.id,
            points=1250,
            level=5,
            current_streak=7,
            longest_streak=14,
            badges=['week_warrior', 'heart_hero', 'step_master'],
            achievements={
                'data_logger': {'earned_date': '2024-01-15', 'points': 50},
                'goal_crusher': {'earned_date': '2024-01-14', 'points': 100}
            }
        )
        db.add(gamification)
        
        # Create consent record
        consent = Consent(
            user_id=patient_user.id,
            consent_type='data_sharing',
            is_granted=True,
            consent_text='I consent to sharing my health data for AI analysis and recommendations.',
            granted_at=datetime.utcnow()
        )
        db.add(consent)
        
        # Create audit log entry
        audit_log = AuditLog(
            user_id=patient_user.id,
            action='user_registration',
            details={'ip_address': '127.0.0.1', 'user_agent': 'HealthCoach App'},
            timestamp=datetime.utcnow()
        )
        db.add(audit_log)
        
        # Commit all changes
        db.commit()
        print("✅ Sample data created successfully")
        
        # Print login credentials
        print("\n🔑 Sample Login Credentials:")
        print("Patient: patient_demo / demo123")
        print("Clinician: clinician_demo / demo123")
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def main():
    """Main initialization function"""
    print("🏥 HealthCoach AI - Database Initialization")
    print("=" * 50)
    
    try:
        # Create database and tables
        engine = create_database()
        
        # Create sample data
        create_sample_data(engine)
        
        print("\n✅ Database initialization completed successfully!")
        print("\nNext steps:")
        print("1. Start the backend server: cd backend && source venv/bin/activate && uvicorn app.main:app --reload")
        print("2. Start the frontend: npm run dev")
        print("3. Visit http://localhost:5173 to access the application")
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
