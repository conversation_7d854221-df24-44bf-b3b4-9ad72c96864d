#!/usr/bin/env python3
"""
Test script for AI model integration
Verifies that the PKL models work correctly with SHAP explanations
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.ai_service import AIService

def test_model_loading():
    """Test if the AI models load correctly"""
    print("🧠 Testing AI Model Loading...")
    
    try:
        ai_service = AIService()
        print("✅ AI Service initialized successfully")
        
        # Check if model file exists
        model_path = os.path.join(os.path.dirname(__file__), '..', 'final_lr_xgb_ensemble.pkl')
        if os.path.exists(model_path):
            print(f"✅ Model file found: {model_path}")
        else:
            print(f"❌ Model file not found: {model_path}")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Error loading AI service: {e}")
        return False

def test_feature_preparation():
    """Test feature preparation for model input"""
    print("\n🔧 Testing Feature Preparation...")
    
    try:
        ai_service = AIService()
        
        # Sample health data
        health_data = {
            'age': 35,
            'gender': 'male',
            'bmi': 26.1,  # weight 80kg, height 175cm
            'systolic_bp': 120,
            'diastolic_bp': 80,
            'glucose': 95,
            'heart_rate': 72,
            'activity_level': 'moderate',
            'family_history_diabetes': False,
            'family_history_hypertension': True,
            'smoking': False,
            'alcohol_consumption': 'moderate'
        }
        
        features = ai_service._prepare_features(health_data)
        print(f"✅ Features prepared successfully: {len(features)} features")
        print(f"   Sample features: {list(features.keys())[:5]}...")
        
        return True
    except Exception as e:
        print(f"❌ Error preparing features: {e}")
        return False

def test_risk_prediction():
    """Test risk prediction functionality"""
    print("\n🎯 Testing Risk Prediction...")
    
    try:
        ai_service = AIService()
        
        # Sample health data for a moderate risk patient
        health_data = {
            'age': 45,
            'gender': 'male',
            'bmi': 28.5,
            'systolic_bp': 135,
            'diastolic_bp': 85,
            'glucose': 110,
            'heart_rate': 78,
            'activity_level': 'low',
            'family_history_diabetes': True,
            'family_history_hypertension': True,
            'smoking': False,
            'alcohol_consumption': 'moderate'
        }
        
        # Test diabetes risk prediction
        diabetes_result = ai_service.predict_diabetes_risk(health_data)
        print(f"✅ Diabetes prediction successful:")
        print(f"   Risk Score: {diabetes_result['risk_score']:.3f}")
        print(f"   Risk Level: {diabetes_result['risk_level']}")
        print(f"   Confidence: {diabetes_result['confidence']:.3f}")
        print(f"   Top Risk Factors: {list(diabetes_result['top_risk_factors'].keys())[:3]}")
        
        # Test hypertension risk prediction
        hypertension_result = ai_service.predict_hypertension_risk(health_data)
        print(f"✅ Hypertension prediction successful:")
        print(f"   Risk Score: {hypertension_result['risk_score']:.3f}")
        print(f"   Risk Level: {hypertension_result['risk_level']}")
        print(f"   Confidence: {hypertension_result['confidence']:.3f}")
        print(f"   Top Risk Factors: {list(hypertension_result['top_risk_factors'].keys())[:3]}")
        
        return True
    except Exception as e:
        print(f"❌ Error in risk prediction: {e}")
        return False

def test_shap_explanations():
    """Test SHAP explanations functionality"""
    print("\n🔍 Testing SHAP Explanations...")
    
    try:
        ai_service = AIService()
        
        # Sample health data
        health_data = {
            'age': 55,
            'gender': 'female',
            'bmi': 32.0,
            'systolic_bp': 145,
            'diastolic_bp': 90,
            'glucose': 125,
            'heart_rate': 85,
            'activity_level': 'low',
            'family_history_diabetes': True,
            'family_history_hypertension': True,
            'smoking': True,
            'alcohol_consumption': 'high'
        }
        
        # Get comprehensive risk assessment
        assessment = ai_service.get_comprehensive_risk_assessment(health_data)
        
        print(f"✅ Comprehensive assessment successful:")
        print(f"   Overall Risk Level: {assessment['overall_risk_level']}")
        print(f"   Diabetes Risk: {assessment['diabetes_risk']:.3f}")
        print(f"   Hypertension Risk: {assessment['hypertension_risk']:.3f}")
        print(f"   Confidence Score: {assessment['confidence_score']:.3f}")
        
        # Check SHAP explanations
        if 'shap_explanations' in assessment:
            shap_data = assessment['shap_explanations']
            print(f"   SHAP Explanations Available:")
            
            if 'diabetes' in shap_data:
                diabetes_factors = shap_data['diabetes']
                print(f"     Diabetes factors: {len(diabetes_factors)} features")
                top_diabetes = sorted(diabetes_factors.items(), key=lambda x: abs(x[1]), reverse=True)[:3]
                for factor, impact in top_diabetes:
                    print(f"       {factor}: {impact:+.3f}")
            
            if 'hypertension' in shap_data:
                hypertension_factors = shap_data['hypertension']
                print(f"     Hypertension factors: {len(hypertension_factors)} features")
                top_hypertension = sorted(hypertension_factors.items(), key=lambda x: abs(x[1]), reverse=True)[:3]
                for factor, impact in top_hypertension:
                    print(f"       {factor}: {impact:+.3f}")
        
        return True
    except Exception as e:
        print(f"❌ Error in SHAP explanations: {e}")
        return False

def test_edge_cases():
    """Test edge cases and error handling"""
    print("\n🧪 Testing Edge Cases...")
    
    try:
        ai_service = AIService()
        
        # Test with minimal data
        minimal_data = {
            'age': 30,
            'gender': 'male'
        }
        
        try:
            result = ai_service.predict_diabetes_risk(minimal_data)
            print("✅ Handled minimal data gracefully")
        except Exception as e:
            print(f"⚠️  Minimal data handling: {e}")
        
        # Test with invalid data
        invalid_data = {
            'age': -5,  # Invalid age
            'gender': 'invalid',  # Invalid gender
            'bmi': 100,  # Extreme BMI
        }
        
        try:
            result = ai_service.predict_diabetes_risk(invalid_data)
            print("✅ Handled invalid data gracefully")
        except Exception as e:
            print(f"⚠️  Invalid data handling: {e}")
        
        return True
    except Exception as e:
        print(f"❌ Error in edge case testing: {e}")
        return False

def main():
    """Main test function"""
    print("🏥 HealthCoach AI - Model Testing Suite")
    print("=" * 50)
    
    tests = [
        ("Model Loading", test_model_loading),
        ("Feature Preparation", test_feature_preparation),
        ("Risk Prediction", test_risk_prediction),
        ("SHAP Explanations", test_shap_explanations),
        ("Edge Cases", test_edge_cases)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'='*50}")
    print(f"🧪 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! AI models are working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the model files and dependencies.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
