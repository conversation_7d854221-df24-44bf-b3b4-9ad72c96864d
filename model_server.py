# model_server.py
import os
import joblib
import pandas as pd

DIABETES_KEY = "diabetes"
HYPERTENSION_KEY = "hypertension"

class ModelServer:
    def __init__(self, diabetes_path=None, hypertension_path=None):
        self.paths = {
            DIABETES_KEY: diabetes_path or os.getenv("MODEL_FILE_DIABETES", "model_diabetes.pkl"),
            HYPERTENSION_KEY: hypertension_path or os.getenv("MODEL_FILE_HYPERTENSION", "model_hypertension.pkl")
        }
        self.models = {}
        self.load_models()

    def load_models(self):
        for key, path in self.paths.items():
            if not os.path.exists(path):
                # Do not crash — keep missing model as None but warn
                print(f"[ModelServer] WARNING: model for '{key}' not found at: {path}")
                self.models[key] = None
            else:
                print(f"[ModelServer] Loading model '{key}' from {path} ...")
                self.models[key] = joblib.load(path)
                print(f"[ModelServer] Loaded '{key}'.")

    def get_model(self, which):
        which = (which or "").lower()
        if which in self.models and self.models[which] is not None:
            return self.models[which]
        raise ValueError(f"Model not available: {which}")

    def _to_dataframe(self, input_dict, expected_cols=None):
        """
        Convert input dict to a 1-row DataFrame.
        If expected_cols provided, reindex to that ordering (fill missing with NaN).
        """
        df = pd.DataFrame([input_dict])
        if expected_cols:
            # keep only expected cols and in same order; fill missing with NaN
            df = df.reindex(columns=expected_cols)
        return df

    def predict(self, which, input_dict, expected_cols=None):
        """
        which: 'diabetes' or 'hypertension'
        input_dict: dict of features the model expects
        expected_cols: optional list of column names (ordered) the model expects
        returns: dict with prediction and (optional) probabilities
        """
        model = self.get_model(which)
        df = self._to_dataframe(input_dict, expected_cols)

        # If the model is a pipeline that includes preprocessing, this will work.
        # If the model expects numpy arrays in a specific order, pass df.values with proper ordering.
        result = {}
        if hasattr(model, "predict_proba"):
            probs = model.predict_proba(df)
            pred = model.predict(df)
            result["prediction"] = int(pred[0]) if hasattr(pred, '__len__') else int(pred)
            # convert numpy types to python native types
            result["probabilities"] = [float(x) for x in probs[0]]
        else:
            pred = model.predict(df)
            result["prediction"] = int(pred[0]) if hasattr(pred, '__len__') else int(pred)

        return result


# singleton
_model_server = None
def get_model_server():
    global _model_server
    if _model_server is None:
        _model_server = ModelServer()
    return _model_server
