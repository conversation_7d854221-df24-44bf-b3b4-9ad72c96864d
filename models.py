# models.py
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from secure_utils import encrypt_text, decrypt_text

db = SQLAlchemy()

class Patient(db.Model):
    __tablename__ = "patients"
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    age = db.Column(db.Integer)
    gender = db.Column(db.String(10))

    # encrypted backing fields (store ciphertext in DB)
    _conditions = db.Column("conditions", db.Text, nullable=True)
    _medications = db.Column("medications", db.Text, nullable=True)
    _allergies = db.Column("allergies", db.Text, nullable=True)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # soft delete support (for data deletion workflow)
    deleted = db.Column(db.Boolean, default=False)
    deleted_at = db.Column(db.DateTime, nullable=True)

    # properties that transparently encrypt/decrypt
    @property
    def conditions(self):
        return decrypt_text(self._conditions) if self._conditions else None

    @conditions.setter
    def conditions(self, val):
        self._conditions = encrypt_text(val) if val is not None else None

    @property
    def medications(self):
        return decrypt_text(self._medications) if self._medications else None

    @medications.setter
    def medications(self, val):
        self._medications = encrypt_text(val) if val is not None else None

    @property
    def allergies(self):
        return decrypt_text(self._allergies) if self._allergies else None

    @allergies.setter
    def allergies(self, val):
        self._allergies = encrypt_text(val) if val is not None else None

    def __repr__(self):
        return f"<Patient id={self.id} email={self.email}>"

class Vital(db.Model):
    __tablename__ = "vitals"
    id = db.Column(db.Integer, primary_key=True)
    patient_id = db.Column(db.Integer, db.ForeignKey('patients.id', ondelete='CASCADE'), nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    glucose = db.Column(db.Float)
    bp_sys = db.Column(db.Integer)
    bp_dia = db.Column(db.Integer)
    heart_rate = db.Column(db.Integer)
    steps = db.Column(db.Integer)
    sleep_hours = db.Column(db.Float)

    def __repr__(self):
        return f"<Vital id={self.id} patient_id={self.patient_id} ts={self.timestamp}>"

class FoodLog(db.Model):
    __tablename__ = "food_logs"
    id = db.Column(db.Integer, primary_key=True)
    patient_id = db.Column(db.Integer, db.ForeignKey('patients.id', ondelete='CASCADE'), nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    food_item = db.Column(db.String(255))
    calories = db.Column(db.Integer)
    sodium_mg = db.Column(db.Integer)

    def __repr__(self):
        return f"<FoodLog id={self.id} patient_id={self.patient_id}>"

class Symptom(db.Model):
    __tablename__ = "symptoms"
    id = db.Column(db.Integer, primary_key=True)
    patient_id = db.Column(db.Integer, db.ForeignKey('patients.id', ondelete='CASCADE'), nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    symptom = db.Column(db.String(255))
    severity = db.Column(db.String(50))

    def __repr__(self):
        return f"<Symptom id={self.id} patient_id={self.patient_id} severity={self.severity}>"

class MicroGoal(db.Model):
    __tablename__ = "micro_goals"
    id = db.Column(db.Integer, primary_key=True)
    patient_id = db.Column(db.Integer, db.ForeignKey('patients.id', ondelete='CASCADE'), nullable=False)
    goal_type = db.Column(db.String(50))
    target_value = db.Column(db.Float)
    achieved_value = db.Column(db.Float)
    date = db.Column(db.Date)

    def __repr__(self):
        return f"<MicroGoal id={self.id} patient_id={self.patient_id} type={self.goal_type}>"

class Gamification(db.Model):
    __tablename__ = "gamification"
    id = db.Column(db.Integer, primary_key=True)
    patient_id = db.Column(db.Integer, db.ForeignKey('patients.id', ondelete='CASCADE'), nullable=False)
    points = db.Column(db.Integer, default=0)
    badges = db.Column(db.Integer, default=0)
    streaks = db.Column(db.Integer, default=0)

    def __repr__(self):
        return f"<Gamification id={self.id} patient_id={self.patient_id} points={self.points}>"

# Consent model (records consent grants/revocations)
class Consent(db.Model):
    __tablename__ = "consents"
    id = db.Column(db.BigInteger, primary_key=True)
    patient_id = db.Column(db.Integer, db.ForeignKey('patients.id', ondelete='CASCADE'), nullable=False)
    consent_version = db.Column(db.String(50), nullable=False)
    consent_text = db.Column(db.Text, nullable=False)
    granted = db.Column(db.Boolean, default=True, nullable=False)
    granted_at = db.Column(db.DateTime, default=datetime.utcnow)
    revoked_at = db.Column(db.DateTime, nullable=True)
    meta = db.Column(db.Text)

    def __repr__(self):
        return f"<Consent id={self.id} patient_id={self.patient_id} version={self.consent_version}>"

# Audit log (append-only)
class AuditLog(db.Model):
    __tablename__ = "audit_logs"
    id = db.Column(db.BigInteger, primary_key=True)
    actor_id = db.Column(db.Integer, nullable=True)
    actor_role = db.Column(db.String(50), nullable=True)
    action = db.Column(db.String(100), nullable=False)
    target_table = db.Column(db.String(100), nullable=True)
    target_id = db.Column(db.String(100), nullable=True)
    details = db.Column(db.Text, nullable=True)        # store JSON string of metadata (avoid PHI here)
    details_hash = db.Column(db.String(128), nullable=True)
    ip = db.Column(db.String(50), nullable=True)
    user_agent = db.Column(db.String(512), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f"<AuditLog id={self.id} action={self.action} actor={self.actor_id}>"
