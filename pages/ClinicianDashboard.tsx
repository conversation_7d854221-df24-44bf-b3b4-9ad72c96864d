// src/pages/ClinicianDashboard.tsx
import React, { useState } from "react";
import { apiFetch } from "../services/api";

type Props = { user:any; onNavigate: (p:string) => void; };

export const ClinicianDashboard: React.FC<Props> = ({ user, onNavigate }) => {
  const [pid,setPid] = useState<number|undefined>();
  const [patient, setPatient] = useState<any>(null);
  const [status,setStatus] = useState<string|null>(null);

  const lookup = async () => {
    if(!pid){ setStatus("Patient id required"); return; }
    setStatus("Loading...");
    try {
      const res = await apiFetch(`/patients/${pid}`, { method: "GET" });
      setPatient(res);
      setStatus(null);
    } catch(err:any){ setStatus(err?.error || JSON.stringify(err)); }
  };

  return (
    <div style={{maxWidth:900, margin:"2rem auto"}}>
      <div style={{background:"white", padding:16, borderRadius:10}}>
        <h3>Clinician Dashboard</h3>
        <label>Patient ID <input type="number" onChange={e=>setPid(e.target.value ? Number(e.target.value) : undefined)} /></label>
        <div style={{display:"flex",gap:8, marginTop:8}}>
          <button onClick={lookup}>Lookup Patient</button>
        </div>
        <div style={{marginTop:8,color:"#64748b"}}>{status}</div>
        {patient && <pre style={{textAlign:"left", marginTop:8}}>{JSON.stringify(patient, null, 2)}</pre>}
      </div>
    </div>
  );
};
