// src/pages/Login.tsx
import React, { useState } from "react";
import { login } from "../services/auth";

type Props = {
  onLogin: (type: 'patient'|'clinician', userData:any) => void;
  onNavigateToSignup: () => void;
};

export const Login: React.FC<Props> = ({ onLogin, onNavigateToSignup }) => {
  const [email,setEmail] = useState("");
  const [password,setPassword] = useState("");
  const [status,setStatus] = useState<string | null>(null);

  const handle = async () => {
    setStatus("Logging in...");
    try {
      const r = await login({email,password});
      onLogin('patient', r);
      setStatus("Logged in");
    } catch(err:any) {
      setStatus(err?.error || err?.message || JSON.stringify(err));
    }
  };

  return (
    <div style={{maxWidth:720, margin:"2rem auto", padding:20}}>
      <div style={{background:"white", padding:20, borderRadius:10}}>
        <h2>Login</h2>
        <label>Email <input value={email} onChange={e=>setEmail(e.target.value)} /></label><br/>
        <label>Password <input type="password" value={password} onChange={e=>setPassword(e.target.value)} /></label><br/>
        <div style={{display:"flex",gap:8,marginTop:8}}>
          <button onClick={handle}>Login</button>
          <button onClick={onNavigateToSignup} style={{background:"#64748b"}}>Signup</button>
        </div>
        <div style={{marginTop:8,color:"#64748b"}}>{status}</div>
      </div>
    </div>
  );
};
