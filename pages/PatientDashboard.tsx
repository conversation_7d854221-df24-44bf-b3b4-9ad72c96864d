// src/pages/PatientDashboard.tsx
import React, { useState } from "react";
import { apiFetch } from "../services/api";

type Props = { user:any; onNavigate: (p:string) => void; };

export const PatientDashboard: React.FC<Props> = ({ user, onNavigate }) => {
  const [pid, setPid] = useState(user?.id ?? "");
  const [glucose,setGlucose] = useState<number|undefined>();
  const [bpSys,setBpSys] = useState<number|undefined>();
  const [bpDia,setBpDia] = useState<number|undefined>();
  const [hr,setHr] = useState<number|undefined>();
  const [sleep,setSleep] = useState<number|undefined>();
  const [status,setStatus] = useState<string|null>(null);
  const [vitals, setVitals] = useState<any[]>([]);

  const addVitals = async () => {
    if(!pid){ setStatus("Patient ID required"); return; }
    setStatus("Saving...");
    try{
      const res = await apiFetch(`/patients/${pid}/vitals`, { method: "POST", body: {
        glucose, bp_sys: bpSys, bp_dia: bpDia, heart_rate: hr, sleep_hours: sleep
      }});
      setStatus("Saved id=" + res.id);
    }catch(err:any){ setStatus(err?.error || JSON.stringify(err)); }
  };

  const loadVitals = async () => {
    if(!pid){ setStatus("Patient ID required"); return; }
    setStatus("Loading...");
    try{
      const data = await apiFetch(`/patients/${pid}/vitals?limit=50`, { method: "GET" });
      setVitals(data);
      setStatus(null);
    }catch(err:any){ setStatus(err?.error || JSON.stringify(err)); }
  };

  const predict = async () => {
    setStatus("Predicting...");
    try{
      const features: any = { age: user?.age ?? undefined, glucose: glucose, bmi: undefined, HbA1c_level: undefined };
      Object.keys(features).forEach(k=> features[k] === undefined && delete features[k]);
      const res = await apiFetch('/predict/diabetes', { method: "POST", body: features });
      setStatus("Prediction: " + JSON.stringify(res));
    }catch(err:any){ setStatus(err?.error || JSON.stringify(err)); }
  };

  return (
    <div style={{maxWidth:1100, margin:"2rem auto"}}>
      <div style={{display:"grid", gridTemplateColumns: "1fr 1fr", gap:12}}>
        <div style={{background:"white", padding:16, borderRadius:10}}>
          <h3>Submit Vitals</h3>
          <label>Patient ID <input value={pid} onChange={e=>setPid(Number(e.target.value)||"")} /></label>
          <label>Glucose <input type="number" onChange={e=>setGlucose(e.target.value ? Number(e.target.value) : undefined)} /></label>
          <label>BP Sys <input type="number" onChange={e=>setBpSys(e.target.value ? Number(e.target.value) : undefined)} /></label>
          <label>BP Dia <input type="number" onChange={e=>setBpDia(e.target.value ? Number(e.target.value) : undefined)} /></label>
          <label>Heart rate <input type="number" onChange={e=>setHr(e.target.value ? Number(e.target.value) : undefined)} /></label>
          <label>Sleep (hours) <input type="number" step="0.1" onChange={e=>setSleep(e.target.value ? Number(e.target.value) : undefined)} /></label>
          <div style={{display:"flex",gap:8, marginTop:8}}>
            <button onClick={addVitals}>Add Vitals</button>
            <button onClick={loadVitals} style={{background:"#64748b"}}>Load Last Vitals</button>
            <button onClick={predict} style={{background:"#0ea5a4"}}>Predict Diabetes</button>
          </div>
          <div style={{marginTop:8,color:"#64748b"}}>{status}</div>
        </div>

        <div style={{background:"white", padding:16, borderRadius:10}}>
          <h3>Recent Vitals</h3>
          {vitals.length===0 ? <div className="muted">No data loaded. Click "Load Last Vitals".</div> :
            <table style={{width:"100%", borderCollapse:"collapse"}}>
              <thead><tr><th>Time</th><th>Glu</th><th>BP</th><th>HR</th><th>Sleep</th></tr></thead>
              <tbody>
                {vitals.map((r:any)=>(
                  <tr key={r.id}>
                    <td>{new Date(r.timestamp).toLocaleString()}</td>
                    <td>{r.glucose ?? ""}</td>
                    <td>{(r.bp_sys??"") + (r.bp_sys ? "/" : "") + (r.bp_dia ?? "")}</td>
                    <td>{r.heart_rate ?? ""}</td>
                    <td>{r.sleep_hours ?? ""}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          }
        </div>
      </div>
    </div>
  );
};
