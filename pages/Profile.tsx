// src/pages/Profile.tsx
import React from "react";
import { logout } from "../services/auth";

type Props = {
  user:any;
  userType: 'patient'|'clinician'|null;
  onNavigateToDashboard: () => void;
  onNavigate: (p:string) => void;
};

export default function Profile({ user, userType, onNavigateToDashboard, onNavigate }: Props) {
  return (
    <div style={{maxWidth:900, margin:"2rem auto"}}>
      <div style={{background:"white", padding:20, borderRadius:10}}>
        <h2>Profile</h2>
        <pre style={{textAlign:"left"}}>{JSON.stringify(user, null, 2)}</pre>
        <div style={{display:"flex",gap:8, marginTop:8}}>
          <button onClick={onNavigateToDashboard}>Go to Dashboard</button>
          <button onClick={() => { logout(); onNavigate('login'); }} style={{background:"#ef4444", color:"#fff"}}>Logout</button>
        </div>
      </div>
    </div>
  );
}
