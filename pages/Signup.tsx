// src/pages/Signup.tsx
import React, { useState } from "react";
import { register } from "../services/auth";

type Props = {
  onSignup: (type:'patient'|'clinician', userData:any) => void;
  onNavigateToLogin: () => void;
};

export const Signup: React.FC<Props> = ({ onSignup, onNavigateToLogin }) => {
  const [name,setName] = useState("");
  const [email,setEmail] = useState("");
  const [password,setPassword] = useState("");
  const [age,setAge] = useState<number|undefined>(undefined);
  const [status,setStatus] = useState<string|null>(null);

  const handle = async () => {
    setStatus("Creating account...");
    try {
      const r = await register({name,email,password,age});
      onSignup('patient', r);
      setStatus("Registered. Now login.");
    } catch(err:any) {
      setStatus(err?.error || err?.message || JSON.stringify(err));
    }
  };

  return (
    <div style={{maxWidth:720, margin:"2rem auto", padding:20}}>
      <div style={{background:"white", padding:20, borderRadius:10}}>
        <h2>Signup</h2>
        <label>Name <input value={name} onChange={e=>setName(e.target.value)} /></label><br/>
        <label>Email <input value={email} onChange={e=>setEmail(e.target.value)} /></label><br/>
        <label>Password <input type="password" value={password} onChange={e=>setPassword(e.target.value)} /></label><br/>
        <label>Age <input type="number" value={age ?? ""} onChange={e=>setAge(e.target.value ? Number(e.target.value) : undefined)} /></label>
        <div style={{display:"flex",gap:8,marginTop:8}}>
          <button onClick={handle}>Create Account</button>
          <button onClick={onNavigateToLogin} style={{background:"#64748b"}}>Back to Login</button>
        </div>
        <div style={{marginTop:8,color:"#64748b"}}>{status}</div>
      </div>
    </div>
  );
};
