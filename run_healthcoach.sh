#!/bin/bash

# HealthCoach AI - Complete Run Script
# This script runs the entire HealthCoach AI application with all components

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}[HEALTHCOACH]${NC} $1"
}

# Cleanup function
cleanup() {
    print_status "Shutting down HealthCoach AI..."

    # Kill background processes
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        print_status "Backend server stopped"
    fi

    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        print_status "Frontend server stopped"
    fi

    print_success "HealthCoach AI stopped successfully"
    exit 0
}

# Trap SIGINT and SIGTERM
trap cleanup SIGINT SIGTERM

# Check if setup has been run
check_setup() {
    print_status "Checking setup requirements..."

    # Check if virtual environment exists
    if [ ! -d "backend/venv" ]; then
        print_error "Virtual environment not found. Please run ./setup.sh first"
        exit 1
    fi

    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        print_error "Node modules not found. Please run ./setup.sh first"
        exit 1
    fi

    # Check if .env file exists
    if [ ! -f "backend/.env" ]; then
        print_error "Backend .env file not found. Please run ./setup.sh first"
        exit 1
    fi

    print_success "Setup requirements satisfied"
}

# Test AI models
test_ai_models() {
    print_header "Testing AI Models..."

    cd backend
    source venv/bin/activate

    if python test_ai_models.py; then
        print_success "AI models tested successfully"
    else
        print_warning "AI model tests failed, but continuing..."
    fi

    cd ..
}

# Initialize database
init_database() {
    print_header "Initializing Database..."

    cd backend
    source venv/bin/activate

    if python init_db.py; then
        print_success "Database initialized successfully"
    else
        print_error "Database initialization failed"
        exit 1
    fi

    cd ..
}

# Start backend server
start_backend() {
    print_header "Starting Backend Server..."

    cd backend
    source venv/bin/activate

    # Start backend in background
    uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload > ../backend.log 2>&1 &
    BACKEND_PID=$!

    # Wait for backend to start
    print_status "Waiting for backend to start..."
    sleep 5

    # Check if backend is running
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        print_success "Backend server started successfully (PID: $BACKEND_PID)"
    else
        print_error "Backend server failed to start"
        cat ../backend.log
        exit 1
    fi

    cd ..
}

# Start frontend server
start_frontend() {
    print_header "Starting Frontend Server..."

    # Start frontend in background
    npm run dev > frontend.log 2>&1 &
    FRONTEND_PID=$!

    # Wait for frontend to start
    print_status "Waiting for frontend to start..."
    sleep 10

    # Check if frontend is running
    if curl -s http://localhost:5173 > /dev/null 2>&1; then
        print_success "Frontend server started successfully (PID: $FRONTEND_PID)"
    else
        print_warning "Frontend server may still be starting..."
    fi
}

# Display application info
show_app_info() {
    print_header "HealthCoach AI is now running!"
    echo
    echo "🌐 Application URLs:"
    echo "   Frontend:        http://localhost:5173"
    echo "   Backend API:     http://localhost:8000"
    echo "   API Docs:        http://localhost:8000/docs"
    echo "   Interactive API: http://localhost:8000/redoc"
    echo
    echo "🔑 Sample Login Credentials:"
    echo "   Patient:    patient_demo / demo123"
    echo "   Clinician:  clinician_demo / demo123"
    echo
    echo "📊 Key Features Available:"
    echo "   ✅ AI Risk Assessment (Diabetes & Hypertension)"
    echo "   ✅ SHAP Explainable AI"
    echo "   ✅ Personalized Recommendations"
    echo "   ✅ Gamification System"
    echo "   ✅ Voice Assistant Integration"
    echo "   ✅ Wearable Device Support"
    echo "   ✅ PDF Report Generation"
    echo "   ✅ Clinician Dashboard"
    echo "   ✅ Patient Portal"
    echo
    echo "📝 Logs:"
    echo "   Backend:  tail -f backend.log"
    echo "   Frontend: tail -f frontend.log"
    echo
    echo "🛑 To stop the application, press Ctrl+C"
    echo
}

# Main function
main() {
    print_header "🏥 HealthCoach AI - Complete Application Launcher"
    echo "================================================================"

    # Check setup
    check_setup

    # Ask user what to do
    echo
    echo "What would you like to do?"
    echo "1) Full setup (initialize database + test models + start servers)"
    echo "2) Quick start (start servers only)"
    echo "3) Test AI models only"
    echo "4) Initialize database only"
    echo
    read -p "Enter your choice (1-4): " choice

    case $choice in
        1)
            print_header "Running full setup..."
            init_database
            test_ai_models
            start_backend
            start_frontend
            show_app_info
            ;;
        2)
            print_header "Quick start..."
            start_backend
            start_frontend
            show_app_info
            ;;
        3)
            print_header "Testing AI models..."
            test_ai_models
            print_success "AI model testing completed"
            exit 0
            ;;
        4)
            print_header "Initializing database..."
            init_database
            print_success "Database initialization completed"
            exit 0
            ;;
        *)
            print_error "Invalid choice. Please run the script again."
            exit 1
            ;;
    esac

    # Wait for user to stop the application
    print_status "Application is running. Press Ctrl+C to stop."

    # Keep the script running
    while true; do
        sleep 1
    done
}

# Run main function
main "$@"