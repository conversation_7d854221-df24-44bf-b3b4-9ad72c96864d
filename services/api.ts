// src/services/api.ts
export const API_BASE = (import.meta.env.VITE_API_BASE || "http://127.0.0.1:8000").replace(/\/$/, "");

export type ApiOptions = {
  method?: string;
  body?: any;
  headers?: Record<string,string>;
  auth?: boolean; // attach Authorization header
};

function getToken(): string | null {
  return localStorage.getItem("welldoc_token");
}

export async function apiFetch(path: string, opts: ApiOptions = {}) {
  const url = API_BASE + path;
  const headers: Record<string,string> = Object.assign({}, opts.headers || {});
  if (!(opts.body instanceof FormData)) headers["Content-Type"] = headers["Content-Type"] || "application/json";
  if (opts.auth !== false) {
    const token = getToken();
    if (token) headers["Authorization"] = "Bearer " + token;
  }

  const fetchOpts: RequestInit = {
    method: opts.method || (opts.body ? "POST" : "GET"),
    headers,
  };
  if (opts.body) fetchOpts.body = (headers["Content-Type"] === "application/json" && !(opts.body instanceof FormData)) ? JSON.stringify(opts.body) : opts.body;

  const res = await fetch(url, fetchOpts);
  const text = await res.text();
  let json;
  try { json = text ? JSON.parse(text) : null; } catch(e) { json = { raw: text }; }
  if (!res.ok) {
    const err = json?.error || json || { status: res.status, text };
    throw err;
  }
  return json;
}
