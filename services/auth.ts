// src/services/auth.ts
import { apiFetch } from "./api";

export async function register(data: {name:string,email:string,password:string,age?:number,gender?:string}) {
  return apiFetch("/register", { method: "POST", body: data, auth: false });
}

export async function login(creds: {email:string,password:string}) {
  const res = await apiFetch("/login", { method: "POST", body: creds, auth:false });
  // backend might return access_token or api_key
  const token = res.access_token || res.api_key || res.token || res.accessToken;
  if (!token) throw new Error("Login succeeded but no token returned from backend");
  localStorage.setItem("welldoc_token", token);
  return res;
}

export function logout() {
  localStorage.removeItem("welldoc_token");
}
