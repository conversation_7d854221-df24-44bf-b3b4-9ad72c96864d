#!/bin/bash

# HealthCoach AI - One-Shot Setup Script
# This script sets up the entire HealthCoach AI application

set -e  # Exit on any error

echo "🏥 HealthCoach AI - Complete Setup Script"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on macOS or Linux
if [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
    print_status "Detected macOS"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
    print_status "Detected Linux"
else
    print_error "Unsupported operating system: $OSTYPE"
    exit 1
fi

# Check for required tools
check_requirements() {
    print_status "Checking system requirements..."
    
    # Check for Python 3.8+
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        print_success "Python found: $PYTHON_VERSION"
    else
        print_error "Python 3 is required but not installed"
        exit 1
    fi
    
    # Check for Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js found: $NODE_VERSION"
    else
        print_error "Node.js is required but not installed"
        exit 1
    fi
    
    # Check for npm
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        print_success "npm found: $NPM_VERSION"
    else
        print_error "npm is required but not installed"
        exit 1
    fi
    
    # Check for MySQL (optional)
    if command -v mysql &> /dev/null; then
        print_success "MySQL found"
    else
        print_warning "MySQL not found. You'll need to install and configure MySQL manually"
    fi
}

# Setup Python virtual environment
setup_python_env() {
    print_status "Setting up Python virtual environment..."
    
    cd backend
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        print_success "Virtual environment created"
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install requirements
    print_status "Installing Python dependencies..."
    pip install -r requirements.txt
    
    print_success "Python environment setup complete"
    cd ..
}

# Setup Node.js environment
setup_node_env() {
    print_status "Setting up Node.js environment..."
    
    # Install frontend dependencies
    print_status "Installing frontend dependencies..."
    npm install
    
    print_success "Node.js environment setup complete"
}

# Setup database
setup_database() {
    print_status "Setting up database..."
    
    # Check if MySQL is running
    if pgrep -x "mysqld" > /dev/null; then
        print_success "MySQL is running"
    else
        print_warning "MySQL is not running. Please start MySQL service"
        if [[ "$OS" == "macos" ]]; then
            print_status "On macOS, you can start MySQL with: brew services start mysql"
        elif [[ "$OS" == "linux" ]]; then
            print_status "On Linux, you can start MySQL with: sudo systemctl start mysql"
        fi
        
        read -p "Press Enter after starting MySQL service..."
    fi
    
    # Create database and user
    print_status "Creating database and user..."
    
    read -p "Enter MySQL root password: " -s MYSQL_ROOT_PASSWORD
    echo
    
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
CREATE DATABASE IF NOT EXISTS healthcoach_db;
CREATE USER IF NOT EXISTS 'healthcoach_user'@'localhost' IDENTIFIED BY 'healthcoach_password';
GRANT ALL PRIVILEGES ON healthcoach_db.* TO 'healthcoach_user'@'localhost';
FLUSH PRIVILEGES;
EOF
    
    print_success "Database setup complete"
}

# Setup environment files
setup_env_files() {
    print_status "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        print_success "Backend .env file created from example"
        print_warning "Please edit backend/.env with your actual configuration"
    else
        print_warning "Backend .env file already exists"
    fi
    
    # Generate secret key
    SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_urlsafe(32))")
    
    # Update .env file with generated secret key
    if [[ "$OS" == "macos" ]]; then
        sed -i '' "s/your-super-secret-jwt-key-change-this-in-production/$SECRET_KEY/" backend/.env
    else
        sed -i "s/your-super-secret-jwt-key-change-this-in-production/$SECRET_KEY/" backend/.env
    fi
    
    print_success "Environment files configured"
}

# Initialize database tables
init_database() {
    print_status "Initializing database tables..."
    
    cd backend
    source venv/bin/activate
    
    # Run database migrations
    python -c "
from app.database import engine, Base
from app.models import *
Base.metadata.create_all(bind=engine)
print('Database tables created successfully')
"
    
    print_success "Database tables initialized"
    cd ..
}

# Create sample data
create_sample_data() {
    print_status "Creating sample data..."
    
    cd backend
    source venv/bin/activate
    
    python -c "
import sys
sys.path.append('.')
from app.database import SessionLocal
from app.models import User, Profile, UserType
from app.auth import get_password_hash

db = SessionLocal()

# Create sample patient user
patient_user = User(
    username='patient_demo',
    email='<EMAIL>',
    password_hash=get_password_hash('demo123'),
    user_type=UserType.PATIENT
)
db.add(patient_user)
db.commit()
db.refresh(patient_user)

# Create profile for patient
patient_profile = Profile(
    user_id=patient_user.id,
    name='Demo Patient',
    age=35,
    gender='male',
    height_cm=175.0,
    weight_kg=80.0,
    activity_level='moderate'
)
db.add(patient_profile)

# Create sample clinician user
clinician_user = User(
    username='clinician_demo',
    email='<EMAIL>',
    password_hash=get_password_hash('demo123'),
    user_type=UserType.CLINICIAN
)
db.add(clinician_user)
db.commit()
db.refresh(clinician_user)

# Create profile for clinician
clinician_profile = Profile(
    user_id=clinician_user.id,
    name='Dr. Demo Clinician'
)
db.add(clinician_profile)

db.commit()
db.close()

print('Sample users created:')
print('Patient: patient_demo / demo123')
print('Clinician: clinician_demo / demo123')
"
    
    print_success "Sample data created"
    cd ..
}

# Start services
start_services() {
    print_status "Starting services..."
    
    # Create start script
    cat > start_services.sh << 'EOF'
#!/bin/bash

# Start backend
echo "Starting backend server..."
cd backend
source venv/bin/activate
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload &
BACKEND_PID=$!
cd ..

# Start frontend
echo "Starting frontend server..."
npm run dev &
FRONTEND_PID=$!

echo "Services started!"
echo "Backend: http://localhost:8000"
echo "Frontend: http://localhost:5173"
echo "API Docs: http://localhost:8000/docs"

# Function to cleanup on exit
cleanup() {
    echo "Stopping services..."
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    exit 0
}

# Trap SIGINT and SIGTERM
trap cleanup SIGINT SIGTERM

# Wait for processes
wait
EOF
    
    chmod +x start_services.sh
    print_success "Start script created: ./start_services.sh"
}

# Main setup function
main() {
    print_status "Starting HealthCoach AI setup..."
    
    # Check requirements
    check_requirements
    
    # Setup environments
    setup_python_env
    setup_node_env
    
    # Setup database
    read -p "Do you want to setup the database? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        setup_database
        setup_env_files
        init_database
        
        read -p "Do you want to create sample data? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            create_sample_data
        fi
    else
        setup_env_files
        print_warning "Database setup skipped. Please configure manually."
    fi
    
    # Create start script
    start_services
    
    print_success "Setup complete!"
    echo
    echo "🎉 HealthCoach AI is ready!"
    echo "=========================="
    echo
    echo "To start the application:"
    echo "  ./start_services.sh"
    echo
    echo "URLs:"
    echo "  Frontend: http://localhost:5173"
    echo "  Backend API: http://localhost:8000"
    echo "  API Documentation: http://localhost:8000/docs"
    echo
    echo "Sample Login Credentials:"
    echo "  Patient: patient_demo / demo123"
    echo "  Clinician: clinician_demo / demo123"
    echo
    echo "Next steps:"
    echo "1. Edit backend/.env with your API keys (OpenAI, wearable providers)"
    echo "2. Configure wearable integrations if needed"
    echo "3. Run ./start_services.sh to start the application"
    echo
    print_success "Happy health coaching! 🏥✨"
}

# Run main function
main "$@"
