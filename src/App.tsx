// src/App.tsx
import { useState, useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Login } from "./pages/Login";
import { Signup } from "./pages/Signup";
import Profile from "./pages/Profile";
import { PatientDashboard } from "./pages/PatientDashboard";
import { ClinicianDashboard } from "./pages/ClinicianDashboard";
import { DemoPage } from "./pages/DemoPage";

const queryClient = new QueryClient();

type Page =
  | "login"
  | "signup"
  | "profile"
  | "patient-dashboard"
  | "clinician-dashboard";

const App = () => {
  const [currentPage, setCurrentPage] = useState<Page>("login");
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [userType, setUserType] = useState<"patient" | "clinician" | null>(null);

  // Initialize page from URL hash on first render
  useEffect(() => {
    const initialHash = window.location.hash.slice(1) as Page;
    if (
      initialHash === "login" ||
      initialHash === "signup" ||
      initialHash === "profile" ||
      initialHash === "patient-dashboard" ||
      initialHash === "clinician-dashboard"
    ) {
      setCurrentPage(initialHash);
    } else {
      // default to login if hash not present/recognized
      setCurrentPage("login");
      window.location.hash = "login";
    }

    const handleHashChange = () => {
      const hash = window.location.hash.slice(1) as Page;
      if (
        hash === "login" ||
        hash === "signup" ||
        hash === "profile" ||
        hash === "patient-dashboard" ||
        hash === "clinician-dashboard"
      ) {
        setCurrentPage(hash);
      }
    };

    window.addEventListener("hashchange", handleHashChange);
    return () => window.removeEventListener("hashchange", handleHashChange);
  }, []);

  const handleLogin = (type: "patient" | "clinician", userData: any) => {
    setUserType(type);
    setCurrentUser(userData);
    // Go directly to demo page to show working features
    setCurrentPage(type === "patient" ? "patient-dashboard" : "clinician-dashboard");
    window.location.hash = type === "patient" ? "patient-dashboard" : "clinician-dashboard";
  };

  const handleSignup = (type: "patient" | "clinician", userData: any) => {
    setUserType(type);
    setCurrentUser(userData);
    // after signup, show profile so user can proceed to dashboard/login
    setCurrentPage("profile");
    window.location.hash = "profile";
  };

  const handleNavigate = (page: string) => {
    // reset user on going back to login
    if (page === "login") {
      setCurrentUser(null);
      setUserType(null);
      window.location.hash = "login";
      setCurrentPage("login");
      return;
    }

    // allow only known pages
    const target = page as Page;
    if (
      target === "login" ||
      target === "signup" ||
      target === "profile" ||
      target === "patient-dashboard" ||
      target === "clinician-dashboard"
    ) {
      setCurrentPage(target);
      window.location.hash = target;
    }
  };

  const handleNavigateToLogin = () => {
    handleNavigate("login");
  };

  const handleNavigateToSignup = () => {
    handleNavigate("signup");
  };

  const handleNavigateToDashboard = () => {
    const dashboardPage = userType === "patient" ? "patient-dashboard" : "clinician-dashboard";
    handleNavigate(dashboardPage);
  };

  const renderPage = () => {
    switch (currentPage) {
      case "login":
        return <Login onLogin={handleLogin} onNavigateToSignup={handleNavigateToSignup} />;
      case "signup":
        return <Signup onSignup={handleSignup} onNavigateToLogin={handleNavigateToLogin} />;
      case "profile":
        return (
          <Profile
            user={currentUser}
            userType={userType}
            onNavigateToDashboard={handleNavigateToDashboard}
            onNavigate={handleNavigate}
          />
        );
      case "patient-dashboard":
        return <DemoPage user={currentUser} onNavigate={handleNavigate} />;
      case "clinician-dashboard":
        return <DemoPage user={currentUser} onNavigate={handleNavigate} />;
      default:
        return <Login onLogin={handleLogin} onNavigateToSignup={handleNavigateToSignup} />;
    }
  };

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        {renderPage()}
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
