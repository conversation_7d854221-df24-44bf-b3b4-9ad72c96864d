import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { Heart, Lock, Mail, User, Building } from "lucide-react";

interface LoginProps {
  onLogin: (userType: 'patient' | 'clinician', userData: any) => void;
  onNavigateToSignup?: () => void;
}

export function Login({ onLogin, onNavigateToSignup }: LoginProps) {
  const [loading, setLoading] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);

  const handleLogin = async (userType: 'patient' | 'clinician', formData: FormData) => {
    setLoading(true);

    try {
      const email = formData.get('email') as string;
      const password = formData.get('password') as string;

      // Use demo credentials if provided, otherwise use form data
      const loginData = {
        username: email === '<EMAIL>' ? 'patient_demo' :
                 email === '<EMAIL>' ? 'clinician_demo' : email,
        password: password || 'demo123'
      };

      const response = await fetch('http://localhost:8000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(loginData),
      });

      if (!response.ok) {
        throw new Error('Login failed');
      }

      const data = await response.json();

      // Store token
      localStorage.setItem('token', data.access_token);
      localStorage.setItem('userType', data.user.user_type.toLowerCase());

      const userData = {
        email: data.user.email,
        name: data.user.username,
        id: data.user.id,
        userType: data.user.user_type.toLowerCase()
      };

      onLogin(data.user.user_type.toLowerCase() as 'patient' | 'clinician', userData);
    } catch (error) {
      console.error('Login error:', error);
      // Fallback to demo mode
      const userData = {
        email: formData.get('email'),
        name: userType === 'patient' ? 'Demo Patient' : 'Demo Clinician',
        id: Math.random().toString(36).substr(2, 9),
        userType
      };
      onLogin(userType, userData);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-subtle flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Logo & Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center">
            <div className="h-12 w-12 rounded-xl bg-gradient-health flex items-center justify-center">
              <Heart className="h-6 w-6 text-white" />
            </div>
          </div>
          <div>
            <h1 className="text-3xl font-bold text-foreground">HealthCoach AI</h1>
            <p className="text-muted-foreground">Your personal health companion</p>
          </div>
        </div>

        <Card className="shadow-card">
          <CardHeader className="space-y-1 pb-4">
            <CardTitle className="text-2xl text-center">Welcome back</CardTitle>
            <CardDescription className="text-center">
              Choose your account type to continue
            </CardDescription>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-800">
              <p className="font-medium">Demo Credentials:</p>
              <p>Patient: <EMAIL> / demo123</p>
              <p>Clinician: <EMAIL> / demo123</p>
            </div>
          </CardHeader>
          
          <CardContent>
            <Tabs defaultValue="patient" className="space-y-4">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="patient" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Patient
                </TabsTrigger>
                <TabsTrigger value="clinician" className="flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  Clinician
                </TabsTrigger>
              </TabsList>

              <TabsContent value="patient" className="space-y-4">
                <form onSubmit={(e) => {
                  e.preventDefault();
                  handleLogin('patient', new FormData(e.currentTarget));
                }}>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="patient-email">Email address</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="patient-email"
                          name="email"
                          type="email"
                          placeholder="<EMAIL>"
                          defaultValue="<EMAIL>"
                          className="pl-10"
                          required
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="patient-password">Password</Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="patient-password"
                          name="password"
                          type="password"
                          placeholder="demo123"
                          defaultValue="demo123"
                          className="pl-10"
                          required
                        />
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="patient-remember" 
                          checked={rememberMe}
                          onCheckedChange={(checked) => setRememberMe(checked === true)}
                        />
                        <Label htmlFor="patient-remember" className="text-sm">
                          Remember me
                        </Label>
                      </div>
                      <Button variant="link" className="text-sm px-0">
                        Forgot password?
                      </Button>
                    </div>

                    <Button 
                      type="submit" 
                      className="w-full bg-gradient-health hover:bg-primary-hover"
                      disabled={loading}
                    >
                      {loading ? "Signing in..." : "Sign in as Patient"}
                    </Button>
                  </div>
                </form>
              </TabsContent>

              <TabsContent value="clinician" className="space-y-4">
                <form onSubmit={(e) => {
                  e.preventDefault();
                  handleLogin('clinician', new FormData(e.currentTarget));
                }}>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="clinician-email">Professional email</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="clinician-email"
                          name="email"
                          type="email"
                          placeholder="<EMAIL>"
                          defaultValue="<EMAIL>"
                          className="pl-10"
                          required
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="clinician-password">Password</Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="clinician-password"
                          name="password"
                          type="password"
                          placeholder="demo123"
                          defaultValue="demo123"
                          className="pl-10"
                          required
                        />
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="clinician-remember"
                          checked={rememberMe}
                          onCheckedChange={(checked) => setRememberMe(checked === true)}
                        />
                        <Label htmlFor="clinician-remember" className="text-sm">
                          Remember me
                        </Label>
                      </div>
                      <Button variant="link" className="text-sm px-0">
                        Forgot password?
                      </Button>
                    </div>

                    <Button 
                      type="submit" 
                      className="w-full bg-gradient-health hover:bg-primary-hover"
                      disabled={loading}
                    >
                      {loading ? "Signing in..." : "Sign in as Clinician"}
                    </Button>
                  </div>
                </form>
              </TabsContent>
            </Tabs>

            <div className="mt-6">
              <Separator className="my-4" />
              
              <div className="space-y-2">
                <Button variant="outline" className="w-full">
                  <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                    <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  Continue with Google
                </Button>
                
                <p className="text-xs text-center text-muted-foreground">
                  Don't have an account?{" "}
                  <Button variant="link" className="text-xs px-0" onClick={onNavigateToSignup}>
                    Sign up here
                  </Button>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="text-center text-xs text-muted-foreground">
          <p>By signing in, you agree to our Terms of Service and Privacy Policy</p>
        </div>
      </div>
    </div>
  );
}