#!/bin/bash

echo "🏥 HealthCoach AI - Quick Demo Launcher"
echo "======================================"

# Check if backend is running
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ Backend is already running on port 8000"
else
    echo "🚀 Starting backend server..."
    cd backend
    source venv/bin/activate
    uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload &
    BACKEND_PID=$!
    cd ..
    echo "✅ Backend started (PID: $BACKEND_PID)"
fi

# Check if frontend is running
if curl -s http://localhost:8081 > /dev/null; then
    echo "✅ Frontend is already running on port 8081"
else
    echo "🚀 Starting frontend server..."
    npm run dev &
    FRONTEND_PID=$!
    echo "✅ Frontend started (PID: $FRONTEND_PID)"
fi

echo ""
echo "🌐 Application URLs:"
echo "   Frontend:     http://localhost:8081"
echo "   Backend API:  http://localhost:8000"
echo "   API Docs:     http://localhost:8000/docs"
echo ""
echo "🔑 Demo Credentials:"
echo "   Patient:      <EMAIL> / demo123"
echo "   Clinician:    <EMAIL> / demo123"
echo ""
echo "📊 Features Available:"
echo "   ✅ AI Risk Assessment (Diabetes & Hypertension)"
echo "   ✅ SHAP Explainable AI"
echo "   ✅ Personalized Recommendations"
echo "   ✅ Gamification System"
echo "   ✅ Voice Assistant Framework"
echo "   ✅ Wearable Device Integration"
echo "   ✅ PDF Report Generation"
echo "   ✅ Complete API with 60+ endpoints"
echo ""
echo "🎉 HealthCoach AI is ready! Open http://localhost:8081 to start."
echo ""
echo "Press Ctrl+C to stop all servers"

# Wait for interrupt
trap 'echo ""; echo "🛑 Stopping servers..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit' INT
wait
