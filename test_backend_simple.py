#!/usr/bin/env python3
"""
Simple backend test without database dependency
"""

import sys
import os
import subprocess
import time
import requests

def test_backend_start():
    """Test if backend can start without database"""
    print("🚀 Testing Backend Server Start...")
    
    # Change to backend directory
    backend_dir = os.path.join(os.getcwd(), 'backend')
    
    # Start backend server in background
    try:
        # Start uvicorn server
        process = subprocess.Popen([
            'bash', '-c', 
            'cd backend && source venv/bin/activate && uvicorn app.main:app --host 127.0.0.1 --port 8001 --log-level warning'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print("⏳ Waiting for backend to start...")
        time.sleep(8)  # Give server time to start
        
        # Test if server is responding
        try:
            response = requests.get('http://127.0.0.1:8001/health', timeout=5)
            if response.status_code == 200:
                print("✅ Backend server started successfully!")
                print(f"   Response: {response.json()}")
                
                # Test API docs endpoint
                docs_response = requests.get('http://127.0.0.1:8001/docs', timeout=5)
                if docs_response.status_code == 200:
                    print("✅ API documentation is accessible")
                
                success = True
            else:
                print(f"❌ Backend server responded with status {response.status_code}")
                success = False
        except requests.exceptions.RequestException as e:
            print(f"❌ Backend server is not responding: {e}")
            success = False
        
        # Stop the server
        process.terminate()
        process.wait(timeout=5)
        
        return success
        
    except Exception as e:
        print(f"❌ Error starting backend server: {e}")
        return False

def test_frontend_build():
    """Test if frontend can build"""
    print("\n⚛️  Testing Frontend Build...")
    
    try:
        # Test if we can build the frontend
        result = subprocess.run(['npm', 'run', 'build'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ Frontend builds successfully!")
            return True
        else:
            print(f"❌ Frontend build failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Frontend build timed out")
        return False
    except Exception as e:
        print(f"❌ Error building frontend: {e}")
        return False

def main():
    """Main test function"""
    print("🏥 HealthCoach AI - Simple Integration Test")
    print("=" * 50)
    
    tests = [
        ("Backend Server", test_backend_start),
        ("Frontend Build", test_frontend_build)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED\n")
            else:
                print(f"❌ {test_name} FAILED\n")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}\n")
    
    print("=" * 50)
    print(f"🧪 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application is ready to run.")
        print("\nNext steps:")
        print("1. Configure database connection in backend/.env")
        print("2. Run: cd backend && source venv/bin/activate && uvicorn app.main:app --reload")
        print("3. Run: npm run dev (in another terminal)")
        print("4. Visit: http://localhost:5173")
        return True
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
