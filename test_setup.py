#!/usr/bin/env python3
"""
Quick setup test script to verify all components are working
"""

import sys
import os
import subprocess
from pathlib import Path

def test_python_version():
    """Test Python version"""
    print("🐍 Testing Python version...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is supported")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not supported (need 3.8+)")
        return False

def test_node_version():
    """Test Node.js version"""
    print("\n📦 Testing Node.js version...")
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Node.js {version} is available")
            return True
        else:
            print("❌ Node.js is not available")
            return False
    except FileNotFoundError:
        print("❌ Node.js is not installed")
        return False

def test_mysql_availability():
    """Test MySQL availability"""
    print("\n🗄️  Testing MySQL availability...")
    try:
        result = subprocess.run(['mysql', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ MySQL is available: {version}")
            return True
        else:
            print("❌ MySQL is not available")
            return False
    except FileNotFoundError:
        print("❌ MySQL is not installed")
        return False

def test_file_structure():
    """Test if all required files exist"""
    print("\n📁 Testing file structure...")
    
    required_files = [
        'backend/app/main.py',
        'backend/requirements.txt',
        'backend/.env.example',
        'src/App.tsx',
        'package.json',
        'final_lr_xgb_ensemble.pkl',
        'setup.sh',
        'run_healthcoach.sh'
    ]
    
    missing_files = []
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def test_backend_imports():
    """Test if backend imports work"""
    print("\n🔧 Testing backend imports...")
    
    # Change to backend directory
    original_cwd = os.getcwd()
    try:
        os.chdir('backend')
        
        # Test basic imports
        test_imports = [
            'import fastapi',
            'import sqlalchemy',
            'import pydantic',
            'import pandas',
            'import sklearn',
            'import xgboost',
            'import shap',
            'from pydantic_settings import BaseSettings'
        ]
        
        for import_stmt in test_imports:
            try:
                exec(import_stmt)
                print(f"✅ {import_stmt}")
            except ImportError as e:
                print(f"❌ {import_stmt} - {e}")
                return False
        
        return True
    except Exception as e:
        print(f"❌ Backend import test failed: {e}")
        return False
    finally:
        os.chdir(original_cwd)

def test_frontend_dependencies():
    """Test if frontend dependencies are available"""
    print("\n⚛️  Testing frontend dependencies...")
    
    if not Path('node_modules').exists():
        print("❌ node_modules directory not found. Run 'npm install' first.")
        return False
    
    # Check if key dependencies exist
    key_deps = [
        'node_modules/react',
        'node_modules/typescript',
        'node_modules/vite',
        'node_modules/@types/react'
    ]
    
    for dep in key_deps:
        if Path(dep).exists():
            print(f"✅ {dep}")
        else:
            print(f"❌ {dep} - MISSING")
            return False
    
    return True

def main():
    """Main test function"""
    print("🏥 HealthCoach AI - Setup Verification")
    print("=" * 50)
    
    tests = [
        ("Python Version", test_python_version),
        ("Node.js Version", test_node_version),
        ("MySQL Availability", test_mysql_availability),
        ("File Structure", test_file_structure),
        ("Backend Imports", test_backend_imports),
        ("Frontend Dependencies", test_frontend_dependencies)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            print()
    
    print("=" * 50)
    print(f"🧪 Setup Verification Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your setup is ready.")
        print("\nNext steps:")
        print("1. Run './setup.sh' to initialize the environment")
        print("2. Run './run_healthcoach.sh' to start the application")
        return True
    else:
        print("⚠️  Some tests failed. Please address the issues above.")
        print("\nCommon solutions:")
        print("- Install missing dependencies")
        print("- Run 'npm install' for frontend dependencies")
        print("- Ensure MySQL is installed and running")
        print("- Check Python version (need 3.8+)")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
