# utils.py
import os
from werkzeug.security import generate_password_hash, check_password_hash
from functools import wraps
from flask import request, jsonify, current_app

def hash_password(password: str) -> str:
    return generate_password_hash(password)

def verify_password(hash_pw: str, password: str) -> bool:
    return check_password_hash(hash_pw, password)

# Very small token-auth shim (for demo). Replace with JWT for production.
def require_api_key(fn):
    @wraps(fn)
    def wrapper(*args, **kwargs):
        key = request.headers.get("x-api-key") or request.args.get("api_key")
        if not key or key != os.getenv("SECRET_KEY"):
            return jsonify({"error":"unauthorized"}), 401
        return fn(*args, **kwargs)
    return wrapper
