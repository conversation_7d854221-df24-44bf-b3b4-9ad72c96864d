Traceback (most recent call last):
  File "/Users/<USER>/Downloads/well-main/backend/venv/lib/python3.11/site-packages/pydantic_settings/sources.py", line 552, in __call__
    field_value = self.prepare_field_value(field_name, field, field_value, value_is_complex)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/well-main/backend/venv/lib/python3.11/site-packages/pydantic_settings/sources.py", line 764, in prepare_field_value
    raise e
  File "/Users/<USER>/Downloads/well-main/backend/venv/lib/python3.11/site-packages/pydantic_settings/sources.py", line 761, in prepare_field_value
    value = self.decode_complex_value(field_name, field, value)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/well-main/backend/venv/lib/python3.11/site-packages/pydantic_settings/sources.py", line 312, in decode_complex_value
    return json.loads(value)
           ^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.11/json/__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.11/json/decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.11/json/decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "<string>", line 2, in <module>
  File "/Users/<USER>/Downloads/well-main/backend/app/database.py", line 4, in <module>
    from .config import settings
  File "/Users/<USER>/Downloads/well-main/backend/app/config.py", line 26, in <module>
    settings = Settings()
               ^^^^^^^^^^
  File "/Users/<USER>/Downloads/well-main/backend/venv/lib/python3.11/site-packages/pydantic_settings/main.py", line 168, in __init__
    **__pydantic_self__._settings_build_values(
      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/well-main/backend/venv/lib/python3.11/site-packages/pydantic_settings/main.py", line 386, in _settings_build_values
    source_state = source()
                   ^^^^^^^^
  File "/Users/<USER>/Downloads/well-main/backend/venv/lib/python3.11/site-packages/pydantic_settings/sources.py", line 554, in __call__
    raise SettingsError(
pydantic_settings.sources.SettingsError: error parsing value for field "allowed_origins" from source "EnvSettingsSource"
(base) keerthana@keerthanas-MacBook-Pro well-main % # utils_audit.py
import hashlib, json
from models import AuditLog, db
from datetime import datetime
from flask import request

def hash_details(details_obj):
    s = json.dumps(details_obj or {}, sort_keys=True, default=str)
    return hashlib.sha256(s.encode()).hexdigest()

def log_audit(actor_id=None, actor_role=None, action=None, target_table=None, target_id=None, details=None):
    try:
        details = details or {}
        details_hash = hash_details(details)
        a = AuditLog(
            actor_id = actor_id,
            actor_role = actor_role,
            action = action,
            target_table = target_table,
            target_id = str(target_id) if target_id is not None else None,
            details = json.dumps(details),
            details_hash = details_hash,
            ip = request.remote_addr if request else None,
            user_agent = request.headers.get('User-Agent')[:512] if request and request.headers.get('User-Agent') else None,
            created_at = datetime.utcnow()
        )
        db.session.add(a)
        db.session.commit()
    except Exception:
        try:
            db.session.rollback()
        except Exception:
            pass
