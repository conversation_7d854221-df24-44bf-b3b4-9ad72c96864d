# utils_audit.py
import hashlib, json
from models import AuditLog, db
from datetime import datetime
from flask import request

def hash_details(details_obj):
    s = json.dumps(details_obj or {}, sort_keys=True, default=str)
    return hashlib.sha256(s.encode()).hexdigest()

def log_audit(actor_id=None, actor_role=None, action=None, target_table=None, target_id=None, details=None):
    try:
        details = details or {}
        details_hash = hash_details(details)
        a = AuditLog(
            actor_id = actor_id,
            actor_role = actor_role,
            action = action,
            target_table = target_table,
            target_id = str(target_id) if target_id is not None else None,
            details = json.dumps(details),
            details_hash = details_hash,
            ip = request.remote_addr if request else None,
            user_agent = request.headers.get('User-Agent')[:512] if request and request.headers.get('User-Agent') else None,
            created_at = datetime.utcnow()
        )
        db.session.add(a)
        db.session.commit()
    except Exception:
        try:
            db.session.rollback()
        except Exception:
            pass
